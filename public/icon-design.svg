<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#14b8a6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="plateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f0fdf4;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="foodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- 盘子 -->
  <circle cx="256" cy="280" r="160" fill="url(#plateGradient)" stroke="#e5e7eb" stroke-width="2"/>
  
  <!-- 食物元素 -->
  <!-- 主菜 (蛋白质) -->
  <ellipse cx="220" cy="260" rx="45" ry="35" fill="url(#foodGradient)" opacity="0.8"/>
  
  <!-- 蔬菜 -->
  <circle cx="300" cy="240" r="25" fill="#22c55e" opacity="0.7"/>
  <circle cx="320" cy="270" r="20" fill="#16a34a" opacity="0.7"/>
  <circle cx="285" cy="285" r="18" fill="#15803d" opacity="0.7"/>
  
  <!-- 碳水化合物 -->
  <ellipse cx="240" cy="320" rx="35" ry="25" fill="#fbbf24" opacity="0.8"/>
  
  <!-- AI 元素 - 智能分析图标 -->
  <g transform="translate(180, 120)">
    <!-- AI 大脑图标 -->
    <circle cx="48" cy="48" r="40" fill="none" stroke="#ffffff" stroke-width="3" opacity="0.9"/>
    <path d="M20 35 Q48 20 76 35 Q65 55 48 60 Q31 55 20 35 Z" fill="#ffffff" opacity="0.9"/>
    <!-- 神经网络连接点 -->
    <circle cx="35" cy="40" r="3" fill="#ffffff" opacity="0.8"/>
    <circle cx="48" cy="35" r="3" fill="#ffffff" opacity="0.8"/>
    <circle cx="61" cy="40" r="3" fill="#ffffff" opacity="0.8"/>
    <circle cx="42" cy="50" r="3" fill="#ffffff" opacity="0.8"/>
    <circle cx="54" cy="50" r="3" fill="#ffffff" opacity="0.8"/>
    <!-- 连接线 -->
    <line x1="35" y1="40" x2="48" y2="35" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
    <line x1="48" y1="35" x2="61" y2="40" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
    <line x1="35" y1="40" x2="42" y2="50" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
    <line x1="61" y1="40" x2="54" y2="50" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
    <line x1="42" y1="50" x2="54" y2="50" stroke="#ffffff" stroke-width="1.5" opacity="0.6"/>
  </g>
  
  <!-- 营养数据可视化元素 -->
  <g transform="translate(320, 140)">
    <!-- 数据图表 -->
    <rect x="0" y="20" width="8" height="30" fill="#ffffff" opacity="0.8" rx="2"/>
    <rect x="12" y="15" width="8" height="35" fill="#ffffff" opacity="0.8" rx="2"/>
    <rect x="24" y="10" width="8" height="40" fill="#ffffff" opacity="0.8" rx="2"/>
    <rect x="36" y="25" width="8" height="25" fill="#ffffff" opacity="0.8" rx="2"/>
    <!-- 数据点 -->
    <circle cx="4" cy="15" r="2" fill="#ffffff" opacity="0.9"/>
    <circle cx="16" cy="10" r="2" fill="#ffffff" opacity="0.9"/>
    <circle cx="28" cy="5" r="2" fill="#ffffff" opacity="0.9"/>
    <circle cx="40" cy="20" r="2" fill="#ffffff" opacity="0.9"/>
    <!-- 连接线 -->
    <path d="M4 15 L16 10 L28 5 L40 20" stroke="#ffffff" stroke-width="2" fill="none" opacity="0.7"/>
  </g>
  
  <!-- 卡路里符号 -->
  <g transform="translate(256, 180)">
    <text x="0" y="0" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#ffffff" opacity="0.9">kcal</text>
  </g>
</svg>
