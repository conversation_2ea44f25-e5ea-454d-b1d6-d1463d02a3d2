<!DOCTYPE html>
<html>
<head>
    <title>KCal Tracker Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0fdf4; }
        .container { max-width: 800px; margin: 0 auto; }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .icon-item { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        canvas { border: 1px solid #ddd; margin: 10px 0; }
        button { background: #10b981; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #059669; }
        .instructions { background: #fef3c7; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🥗 KCal Tracker 图标生成器</h1>
        
        <div class="instructions">
            <h3>📋 使用说明：</h3>
            <ol>
                <li>点击"生成所有图标"按钮</li>
                <li>右键点击每个图标，选择"另存为图片"</li>
                <li>保存为对应的文件名</li>
                <li>将文件放入项目的 public 目录</li>
            </ol>
        </div>

        <button onclick="generateAllIcons()" style="font-size: 16px; padding: 15px 30px;">🎨 生成所有图标</button>
        <button onclick="downloadAllIcons()" style="font-size: 16px; padding: 15px 30px;">📥 下载所有图标</button>

        <div class="icon-grid" id="iconGrid"></div>
    </div>

    <script>
        const iconSizes = [
            { size: 16, name: 'favicon-16x16.png', title: 'Favicon 16x16' },
            { size: 32, name: 'favicon-32x32.png', title: 'Favicon 32x32' },
            { size: 64, name: 'pwa-64x64.png', title: 'PWA 64x64' },
            { size: 180, name: 'apple-touch-icon.png', title: 'Apple Touch Icon' },
            { size: 192, name: 'pwa-192x192.png', title: 'PWA 192x192' },
            { size: 512, name: 'pwa-512x512.png', title: 'PWA 512x512' }
        ];

        let generatedCanvases = [];

        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 缩放比例
            const scale = size / 32;
            
            // 背景渐变
            const bgGradient = ctx.createLinearGradient(0, 0, size, size);
            bgGradient.addColorStop(0, '#10b981');
            bgGradient.addColorStop(1, '#14b8a6');
            
            // 绘制背景圆形
            ctx.fillStyle = bgGradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 * 0.9, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制盘子
            ctx.fillStyle = 'rgba(255,255,255,0.9)';
            ctx.strokeStyle = '#e5e7eb';
            ctx.lineWidth = Math.max(0.5 * scale, 1);
            ctx.beginPath();
            ctx.arc(size/2, size * 0.56, size * 0.31, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            
            // 绘制食物元素
            // 主菜 (蛋白质)
            ctx.fillStyle = 'rgba(34,197,94,0.8)';
            ctx.beginPath();
            ctx.ellipse(size * 0.44, size * 0.53, size * 0.09, size * 0.06, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 蔬菜
            ctx.fillStyle = 'rgba(34,197,94,0.7)';
            ctx.beginPath();
            ctx.arc(size * 0.59, size * 0.5, size * 0.047, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = 'rgba(22,163,74,0.7)';
            ctx.beginPath();
            ctx.arc(size * 0.625, size * 0.56, size * 0.038, 0, 2 * Math.PI);
            ctx.fill();
            
            // 碳水化合物
            ctx.fillStyle = 'rgba(251,191,36,0.8)';
            ctx.beginPath();
            ctx.ellipse(size * 0.47, size * 0.625, size * 0.078, size * 0.047, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // AI 元素 (大脑图标)
            ctx.strokeStyle = 'rgba(255,255,255,0.9)';
            ctx.lineWidth = Math.max(size * 0.025, 1);
            ctx.beginPath();
            ctx.arc(size * 0.5, size * 0.31, size * 0.094, 0, 2 * Math.PI);
            ctx.stroke();
            
            // AI 神经网络点
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            const points = [
                [size * 0.45, size * 0.3],
                [size * 0.5, size * 0.28],
                [size * 0.55, size * 0.3],
                [size * 0.48, size * 0.34],
                [size * 0.52, size * 0.34]
            ];
            
            points.forEach(([x, y]) => {
                ctx.beginPath();
                ctx.arc(x, y, Math.max(size * 0.0125, 1), 0, 2 * Math.PI);
                ctx.fill();
            });
            
            // 连接线
            ctx.strokeStyle = 'rgba(255,255,255,0.6)';
            ctx.lineWidth = Math.max(size * 0.01, 1);
            ctx.beginPath();
            ctx.moveTo(points[0][0], points[0][1]);
            ctx.lineTo(points[1][0], points[1][1]);
            ctx.lineTo(points[2][0], points[2][1]);
            ctx.moveTo(points[0][0], points[0][1]);
            ctx.lineTo(points[3][0], points[3][1]);
            ctx.lineTo(points[4][0], points[4][1]);
            ctx.lineTo(points[2][0], points[2][1]);
            ctx.stroke();
            
            // kcal 文字 (只在较大尺寸显示)
            if (size >= 64) {
                ctx.fillStyle = 'rgba(255,255,255,0.9)';
                ctx.font = `bold ${Math.max(size * 0.125, 8)}px Arial, sans-serif`;
                ctx.textAlign = 'center';
                ctx.fillText('kcal', size/2, size * 0.78);
            }
            
            return canvas;
        }

        function generateAllIcons() {
            const grid = document.getElementById('iconGrid');
            grid.innerHTML = '';
            generatedCanvases = [];

            iconSizes.forEach(({ size, name, title }) => {
                const canvas = createIcon(size);
                generatedCanvases.push({ canvas, name });

                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const titleEl = document.createElement('h3');
                titleEl.textContent = title;
                titleEl.style.margin = '0 0 10px 0';
                titleEl.style.color = '#059669';
                
                const sizeEl = document.createElement('p');
                sizeEl.textContent = `${size}×${size}px`;
                sizeEl.style.margin = '0 0 10px 0';
                sizeEl.style.color = '#666';
                
                const filenameEl = document.createElement('p');
                filenameEl.textContent = name;
                filenameEl.style.margin = '0 0 15px 0';
                filenameEl.style.fontSize = '12px';
                filenameEl.style.color = '#888';
                filenameEl.style.fontFamily = 'monospace';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = '💾 下载';
                downloadBtn.onclick = () => downloadIcon(canvas, name);
                
                item.appendChild(titleEl);
                item.appendChild(sizeEl);
                item.appendChild(filenameEl);
                item.appendChild(canvas);
                item.appendChild(downloadBtn);
                grid.appendChild(item);
            });
        }

        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function downloadAllIcons() {
            if (generatedCanvases.length === 0) {
                alert('请先生成图标！');
                return;
            }

            generatedCanvases.forEach(({ canvas, name }, index) => {
                setTimeout(() => {
                    downloadIcon(canvas, name);
                }, index * 200); // 延迟下载避免浏览器阻止
            });
        }

        // 页面加载时自动生成图标
        window.onload = generateAllIcons;
    </script>
</body>
</html>
