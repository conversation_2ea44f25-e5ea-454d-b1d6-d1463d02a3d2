// PWA图标生成脚本 - 基于KCal Tracker设计
// 在浏览器控制台中运行此代码

function createPWAIcon(size) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');

    const scale = size / 192;

    // 绘制圆角矩形背景
    ctx.fillStyle = '#22c55e';
    ctx.beginPath();
    ctx.roundRect(0, 0, size, size, 24 * scale);
    ctx.fill();

    // 绘制同心圆环
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 8 * scale;
    ctx.beginPath();
    ctx.arc(96 * scale, 96 * scale, 60 * scale, 0, 2 * Math.PI);
    ctx.stroke();

    ctx.lineWidth = 6 * scale;
    ctx.beginPath();
    ctx.arc(96 * scale, 96 * scale, 40 * scale, 0, 2 * Math.PI);
    ctx.stroke();

    ctx.lineWidth = 4 * scale;
    ctx.beginPath();
    ctx.arc(96 * scale, 96 * scale, 20 * scale, 0, 2 * Math.PI);
    ctx.stroke();

    // 绘制KCal文本
    ctx.fillStyle = 'white';
    ctx.font = `bold ${24 * scale}px Arial, sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('KCal', 96 * scale, 140 * scale);

    return canvas;
}

// 生成所有PWA图标
function generateAllPWAIcons() {
    const sizes = [64, 192, 512];

    sizes.forEach(size => {
        const canvas = createPWAIcon(size);
        downloadIcon(canvas, `pwa-${size}x${size}.png`);
    });
}

// 生成Apple Touch图标
function generateAppleTouchIcon() {
    const canvas = createPWAIcon(180);
    downloadIcon(canvas, 'apple-touch-icon.png');
}

function downloadIcon(canvas, filename) {
    const link = document.createElement('a');
    link.download = filename;
    link.href = canvas.toDataURL('image/png');
    link.click();
}

// 在浏览器控制台中运行以下命令：
// generateAllPWAIcons() - 生成PWA图标
// generateAppleTouchIcon() - 生成Apple Touch图标

console.log('PWA图标生成器已加载');
console.log('运行 generateAllPWAIcons() 生成PWA图标');
console.log('运行 generateAppleTouchIcon() 生成Apple Touch图标');
