# 卡路里追踪移动端Web应用 - 项目概览

## 🎯 项目目标
创建一个功能完整的AI驱动卡路里追踪移动端Web应用，具备智能食物识别、用户档案管理、营养分析和PWA离线功能。

## 🏗️ 技术架构
- **前端框架**: React 18.2+ with TypeScript 5.0+
- **构建工具**: Vite 5.0+ with PWA Plugin
- **状态管理**: Zustand 4.4+ (轻量级，适合移动端)
- **UI框架**: Tailwind CSS 3.4+ + Headless UI
- **数据存储**: sql.js (SQLite in browser) + IndexedDB fallback
- **图表库**: Chart.js 4.0+ (轻量级，移动端友好)
- **相机API**: MediaDevices.getUserMedia() + Canvas API
- **AI服务**: Google Gemini API (gemini-2.0-flash-exp)

## 📱 核心功能
1. **用户档案与卡路里计算系统**
   - 基于Mifflin-St Jeor公式的BMR计算
   - 智能三餐分配（早餐30%、午餐40%、晚餐30%）
   - 个性化减重目标设置

2. **交互式日历仪表板**
   - 颜色编码指示器（红/黄/绿/灰）
   - Chart.js趋势图表
   - 每日详细数据查看

3. **AI食物识别系统**
   - Google Gemini API集成
   - 图片压缩和处理
   - 结构化食物数据解析

4. **食物条目管理**
   - 完整CRUD操作
   - 三餐分类管理
   - 搜索筛选功能

5. **PWA移动端优化**
   - Service Worker离线缓存
   - Web App Manifest
   - 响应式设计（320px-768px）

## 🚀 开发阶段
- **P0（核心功能）**: 项目初始化、用户档案、BMR计算、基础UI
- **P1（主要功能）**: 日历仪表板、食物条目管理、本地存储
- **P2（高级功能）**: AI食物识别、图片处理、离线同步
- **P3（优化功能）**: PWA配置、性能优化、测试覆盖

## 📊 项目状态
- **创建时间**: 2025-01-10
- **当前阶段**: 项目初始化
- **协议版本**: RIPER-5 v5.0