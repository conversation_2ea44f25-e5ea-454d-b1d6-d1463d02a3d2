# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands
- `npm run dev` - Start development server (Vite on localhost:5173)
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint code quality checks
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

### Testing
- `npm run test` - Run tests with Vitest
- `npm run test:ui` - Run tests with UI interface
- `npm run test:coverage` - Run tests with coverage report

### Test Pages
- `/camera-test` - Camera functionality testing
- `/ai-test` - AI food recognition testing

## Architecture Overview

### Domain-Driven Design Structure
The codebase follows a clean architecture with domain-driven design:

```
src/
├── app/                    # Application layer (pages, routing)
├── domains/               # Business domains (user, nutrition, food, analytics)
├── shared/               # Shared utilities and components
└── infrastructure/      # External services (storage, AI)
```

### Key Architectural Patterns

**Domain Separation**: Four main business domains:
- `user/` - User profile management and BMR calculations
- `nutrition/` - Nutrition tracking and analysis
- `food/` - Food recognition and entry management
- `analytics/` - Data visualization and calendar features

**Component Hierarchy**: Atomic design with three levels:
- `atoms/` - Basic UI components (Button, Input, Card, etc.)
- `molecules/` - Composite components (FormField, Modal, Camera, etc.)
- Page-level components in `app/pages/`

**State Management**: Zustand stores in each domain for localized state management with persistence middleware.

**Data Layer**: Repository pattern with IndexedDB storage:
- `BaseRepository.ts` - Abstract base class
- Domain-specific repositories (UserRepository, FoodRepository)
- Database manager with singleton pattern

### Routing & Navigation
Protected route system based on profile completion status:
- Root redirects to `/setup` or `/dashboard` based on profile state
- Route guards prevent access to main app without completed profile
- Test pages accessible when profile is complete

### Key Technologies & Integrations

**AI Integration**: Google Gemini 2.0 Flash for food recognition via structured prompts in `infrastructure/ai/geminiService.ts`

**PWA Configuration**: Vite PWA plugin with service worker, offline caching, and app manifest for native-like experience

**Mobile-First Design**: Tailwind CSS with responsive breakpoints, camera integration using MediaDevices API

**Data Persistence**: IndexedDB with Repository pattern, automatic initialization, and export/import capabilities

## Important Implementation Details

### Environment Variables
- `VITE_GEMINI_API_KEY` - Required for AI food recognition functionality

### Path Aliases
- `@/` - Maps to `src/` directory for clean imports

### Build Optimization
- Manual chunks configuration for vendor, UI, charts, and utilities
- PWA caching strategy for Gemini API calls

### Type Safety
Comprehensive TypeScript types in `shared/types/` covering all domains with strict mode enabled.

### Database Schema
Four main stores: userProfiles, foodEntries, dailySummaries, settings with automatic initialization on app startup.

## UI/UX Framework

### DaisyUI Design System
- **Modern Component Library**: Full DaisyUI integration with Tailwind CSS 4
- **Theme System**: Custom kcal theme with primary/secondary color schemes
- **Form Components**: input-group, steps, card, alert, stat components
- **Button Variants**: Primary, outline, ghost with consistent sizing
- **Layout Components**: Hero sections, navbar, mobile-responsive grids

### Animation System (anime.js 4.0.2)
- **Page Transitions**: Smooth opacity and translate animations
- **Micro-interactions**: Input focus, validation feedback, button states
- **Step Navigation**: Directional slide animations for multi-step forms
- **Progress Indicators**: Animated width changes with easing
- **Error States**: Shake animations for validation failures

**Animation Import Pattern**:
```typescript
import { animate } from 'animejs';
// Usage: animate(element, { ...params })
```

### Mobile-First Responsive Design
- **Breakpoints**: 320px (mobile) → 768px (tablet) → 1024px+ (desktop)
- **Touch Optimization**: Large button areas, gesture support
- **PWA Ready**: Native app-like experience with offline support

## Core Business Logic

### BMR & Nutrition Calculations
- **Mifflin-St Jeor Formula**: Gender-specific BMR calculations in `shared/utils/bmr.ts`
- **Activity Multipliers**: Sedentary (1.2) to Very Active (1.9)
- **Daily Calorie Limits**: Based on weight loss goals with safety checks
- **Meal Distribution**: Configurable breakfast/lunch/dinner ratios

### Weight Loss Safety Validation
- **Speed Limits**: 0.2-1kg per week maximum safe loss rate
- **Goal Validation**: Prevents unrealistic or dangerous targets
- **Progress Tracking**: Week-over-week monitoring

### User Profile Management
- **Three-Step Setup**: Basic info → Goals → Confirmation
- **Data Persistence**: Zustand + IndexedDB automatic sync
- **BMR Recalculation**: Triggered on profile updates

## Data Management

### IndexedDB Storage Architecture
```typescript
// Database stores
- userProfiles: Complete user data with preferences
- foodEntries: Individual meal/food logging entries  
- dailySummaries: Aggregated daily nutrition data
- settings: App configuration and preferences
```

### Repository Pattern Implementation
- **BaseRepository**: Abstract CRUD operations with TypeScript generics
- **Domain Repositories**: Specialized methods for each business domain
- **Database Manager**: Singleton initialization and connection management
- **Export/Import**: Full data backup and restore capabilities

### State Management Strategy
- **Domain-Specific Stores**: Zustand stores per business domain
- **Persistence Middleware**: Automatic localStorage sync for critical data
- **Reactive Updates**: Real-time UI updates on data changes

## Development Guidelines

### Component Development
- **DaisyUI First**: Use native DaisyUI components before custom solutions
- **Atomic Design**: Build from atoms → molecules → pages
- **TypeScript Strict**: Full type coverage with strict mode enabled
- **Animation Integration**: Use anime.js 4 for all motion design

### File Organization Conventions
- **Domain Boundaries**: Keep related code within domain folders
- **Index Exports**: Clean imports via index.ts files
- **Type Definitions**: Centralized in `shared/types/`
- **Utility Functions**: Domain-agnostic helpers in `shared/utils/`

### Code Quality Standards
- **ESLint Configuration**: Extended rules for React and TypeScript
- **Prettier Formatting**: Consistent code style across codebase
- **Husky Pre-commit**: Automated linting and formatting on commit
- **Import Organization**: Path aliases for clean dependency management

## Common Development Tasks

### Adding New UI Components
1. Check DaisyUI documentation for existing components
2. Create in appropriate atomic level (atoms/molecules)
3. Add TypeScript interfaces for props
4. Include animation states if interactive
5. Export via index.ts for clean imports

### Implementing New Domains
1. Create domain folder structure: components/, hooks/, services/, stores/
2. Define TypeScript types in `shared/types/`
3. Implement Zustand store with persistence
4. Add repository layer if data persistence needed
5. Create page components in `app/pages/`

### Database Schema Changes
1. Update type definitions in `shared/types/`
2. Modify repository implementations
3. Handle data migration for existing users
4. Test export/import functionality
5. Update database initialization

### Animation Implementation
1. Import `animate` from 'animejs'
2. Use React refs for DOM element targeting
3. Define animation parameters with TypeScript
4. Consider mobile performance implications
5. Test across different screen sizes