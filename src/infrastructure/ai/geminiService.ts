import { FoodRecognitionResult } from '@/shared/types';

/**
 * Google Gemini AI服务
 */
export class GeminiService {
  private apiKey: string;
  private endpoint: string;
  private model: string;
  private timeout: number;


  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    this.model = import.meta.env.VITE_GEMINI_MODEL || 'gemini-2.5-flash';
    this.timeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');

    // 动态设置API端点
    this.endpoint = this.getApiEndpoint();

    // 添加API端点使用日志
    console.log(`Gemini API端点: ${this.endpoint}`);
    console.log(`Gemini 模型: ${this.model}`);

    if (!this.apiKey) {
      console.warn('Gemini API密钥未配置');
    }
  }

  /**
   * 获取API端点
   */
  private getApiEndpoint(): string {
    // 优先使用环境变量配置的端点
    const envEndpoint = import.meta.env.VITE_GEMINI_API_ENDPOINT;
    if (envEndpoint) {
      console.log('使用环境变量配置的端点:', envEndpoint);
      return envEndpoint;
    }

    // 统一使用代理端点
    console.log('使用代理端点: https://g-proxy.eh.cx');
    return 'https://g-proxy.eh.cx';
  }



  /**
   * 识别食物图片（支持多张图片）
   */
  async recognizeFood(imageFiles: File | File[], additionalContext?: string, userContext?: any): Promise<FoodRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      // 处理单张或多张图片
      const files = Array.isArray(imageFiles) ? imageFiles : [imageFiles];

      // 验证图片数量限制（根据Context7查询结果，Gemini支持多图片）
      if (files.length > 5) {
        throw new Error('最多支持5张图片同时识别');
      }

      // 将所有图片转换为base64
      const base64Images = await Promise.all(
        files.map(file => this.fileToBase64(file))
      );

      // 发送多图片请求
      const response = await this.sendMultiImageRequest(base64Images, additionalContext, userContext);

      // 解析响应
      return this.parseResponse(response, 'image');
    } catch (error) {
      console.error('食物识别失败:', error);
      throw new Error(error instanceof Error ? error.message : '食物识别失败');
    }
  }

  /**
   * 专门的营养建议分析 - 使用更高的温度参数获得更有创造性的建议
   */
  async analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }> {
    if (!this.apiKey) {
      console.error('Gemini API密钥未配置');
      throw new Error('Gemini API密钥未配置');
    }

    console.log('发送营养建议请求到Gemini API...');
    console.log('API端点：', this.endpoint);
    console.log('提示词预览：', prompt.substring(0, 200) + '...');

    try {
      const response = await fetch(`${this.endpoint}/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,  // 更高的温度参数，获得更有创造性的营养建议
          }
        }),
      });

      console.log('API响应状态：', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API请求失败：', response.status, errorText);
        throw new Error(`Gemini API请求失败: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('API响应数据：', JSON.stringify(data, null, 2));

      // 提取AI建议文本
      if (data.candidates && data.candidates.length > 0) {
        const content = data.candidates[0].content;
        console.log('候选内容：', content);

        if (content && content.parts && content.parts.length > 0) {
          const advice = content.parts[0].text;
          console.log('提取的建议文本：', advice);

          return {
            advice: advice?.trim() || '暂无建议',
            rawResponse: data
          };
        } else {
          console.log('内容结构异常：', content);
        }
      } else {
        console.log('无候选响应：', data);
      }

      return {
        advice: '暂无建议',
        rawResponse: data
      };

    } catch (error) {
      console.error('营养建议分析失败:', error);
      console.error('错误详情:', {
        message: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * 分析文字中的食物信息
   */
  async analyzeTextFood(text: string): Promise<FoodRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      // 发送文字分析请求
      const response = await this.sendTextRequest(text);

      // 解析响应
      return this.parseResponse(response, 'text');
    } catch (error) {
      console.error('文字分析失败:', error);
      throw new Error(error instanceof Error ? error.message : '文字分析失败');
    }
  }

  /**
   * 构建食物识别提示词
   */
  private buildFoodRecognitionPrompt(additionalContext?: string): string {
    return `
**重要：必须返回纯JSON格式，不要任何markdown标记或解释文字**

请分析这张图片中的食物。如果图片中没有食物，请返回空的foods数组。

**优先级分析顺序：**
1. **首先检查营养成分表/营养标签**：如果图片中包含营养成分表、营养标签或包装上的营养信息，请优先使用这些准确数据
2. **识别食物类型**：区分包装食品（有标签）和新鲜食品（需要估算）
3. **结合用户提供的额外信息**：如果用户提供了补充描述，请将其作为重要参考
4. **数据来源标注**：明确标注数据来源是营养标签还是视觉估算

**分析要求：**
1. 识别图片中所有可见的食物
2. 对于包装食品：优先读取营养标签上的准确数据
3. 对于新鲜食品：基于视觉估算重量和营养成分
4. 计算每种食物的卡路里（标签优先，否则估算）
5. 提供识别置信度（0-1）
6. 获取详细营养成分信息
${additionalContext ? `\n**用户提供的额外信息：**\n${additionalContext}\n请将此信息作为重要参考，用于修正或补充视觉识别结果。` : ''}

**返回格式：**
{
  "foods": [
    {
      "name": "食物名称",
      "calories": 卡路里数值,
      "weight": 重量(克),
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation",
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 纤维(克),
        "sugar": 糖(克),
        "sodium": 钠(毫克)
      },
      "labelInfo": {
        "hasLabel": true/false,
        "servingSize": "份量信息(如果有标签)",
        "labelAccuracy": 置信度(0-1, 仅当hasLabel为true时)
      }
    }
  ]
}

**特殊处理规则：**
- 营养标签数据：置信度应为0.9-1.0（标签清晰度决定）
- 视觉估算数据：置信度通常为0.6-0.8（基于食物识别清晰度）
- 如果同时有标签和食物，优先使用标签数据，但要估算实际食用分量
- 包装食品要注意区分整包装重量和实际食用分量
- **如果图片中没有食物（如文档、风景、人物等），必须返回空的foods数组**
- **非常重要：只返回纯JSON格式，不要任何markdown代码块标记，不要任何解释文字**

请仔细分析图片，直接返回JSON：
    `.trim();
  }

  /**
   * 构建文字分析提示词
   */
  private buildTextAnalysisPrompt(text: string): string {
    return `
**重要：必须返回纯JSON格式，不要任何markdown标记或解释文字**

请分析以下文字描述中的食物信息。如果文字中没有明确的食物信息，请返回空的foods数组。

用户描述：${text}

要求：
1. 从文字中识别所有提到的食物
2. 根据描述估算每种食物的重量（克）
3. 计算每种食物的卡路里
4. 提供识别置信度（0-1）
5. 估算营养成分（蛋白质、脂肪、碳水化合物、纤维、糖）

返回格式：
{
  "foods": [
    {
      "name": "食物名称",
      "calories": 卡路里数值,
      "weight": 重量(克),
      "confidence": 置信度(0-1),
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 纤维(克),
        "sugar": 糖(克)
      }
    }
  ]
}

注意事项：
- **如果文字中没有明确的食物信息，必须返回空的foods数组**
- 重量估算基于常见食物的标准分量
- 卡路里和营养成分计算基于标准营养数据
- 置信度反映从文字描述中提取信息的准确性
- **非常重要：只返回纯JSON格式，不要任何markdown代码块标记，不要任何解释文字**

直接返回JSON：

    `.trim();
  }

  /**
   * 发送多图片识别API请求（基于Context7最佳实践）
   */
  private async sendMultiImageRequest(base64Images: string[], additionalContext?: string, userContext?: any): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 构建多图片内容数组
      const imageParts = base64Images.map((base64Image) => ({
        inline_data: {
          mime_type: 'image/jpeg', // 根据Context7文档，需要明确指定MIME类型
          data: base64Image
        }
      }));

      // 构建用户上下文信息（用于个性化建议）
      const userContextPrompt = userContext ? `
用户个人信息：
- 年龄：${userContext.age}岁
- 性别：${userContext.gender}
- 体重：${userContext.weight.toFixed(2)}kg
- 身高：${userContext.height}cm
- 目标体重：${userContext.targetWeight.toFixed(2)}kg
- 活动水平：${userContext.activityLevel}
- 基础代谢率：${Math.round(userContext.bmr)} kcal/天
- 总消耗：${Math.round(userContext.tdee)} kcal/天
- 目标天数：${userContext.targetDays}天

请根据以上个人信息提供个性化的营养建议和运动建议。
` : '';

      // 构建完整的提示词
      const prompt = `你是一个专业的营养师和食物识别专家。请分析这${base64Images.length}张图片中的食物，并提供详细的营养信息。

${userContextPrompt}

${additionalContext ? `额外信息：${additionalContext}` : ''}

请按照以下JSON格式返回结果：
{
  "foods": [
    {
      "name": "食物名称",
      "calories": 卡路里数值,
      "weight": 重量(克),
      "confidence": 0.0-1.0之间的置信度,
      "dataSource": "text_analysis",
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠(毫克)
      }
    }
  ],
  "personalizedAdvice": "基于用户个人数据的个性化建议",
  "exerciseAdvice": "针对用户的专属运动建议"
}

要求：
1. 准确识别每张图片中的所有食物
2. 提供准确的营养成分估算
3. 置信度要真实反映识别的准确性
4. 如果有用户个人信息，请提供个性化建议
5. 确保返回有效的JSON格式`;

      // 构建请求体（基于Context7文档的最佳实践）
      const requestBody = {
        contents: [{
          parts: [
            { text: prompt },
            ...imageParts
          ]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 32,
          topP: 1,
          maxOutputTokens: 4096,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      // 构建API URL
      const apiUrl = `${this.endpoint}/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`;

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }

      throw error;
    }
  }



  /**
   * 发送文字分析API请求
   */
  private async sendTextRequest(text: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 使用正确的Gemini API端点和认证方式
      const response = await fetch(`${this.endpoint}/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: this.buildTextAnalysisPrompt(text)
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.1,
            topK: 1,
            topP: 1,
            maxOutputTokens: 2048
          }
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }

      throw error;
    }
  }

  /**
   * 解析API响应
   */
  private parseResponse(response: any, method: 'image' | 'text' = 'image'): FoodRecognitionResult {
    try {
      // 提取生成的文本
      const candidates = response.candidates;
      if (!candidates || candidates.length === 0) {
        throw new Error('API响应格式错误：没有候选结果');
      }

      const content = candidates[0].content;
      if (!content || !content.parts || content.parts.length === 0) {
        throw new Error('API响应格式错误：没有内容');
      }

      const text = content.parts[0].text;
      if (!text) {
        throw new Error('API响应格式错误：没有文本内容');
      }

      // 尝试解析JSON
      let jsonData: any;
      try {
        // 清理文本，移除可能的markdown格式和其他文本
        let cleanText = text;

        // 移除markdown代码块标记
        cleanText = cleanText.replace(/```json\s*/g, '').replace(/```\s*/g, '');

        // 尝试提取JSON对象
        const jsonMatch = cleanText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          cleanText = jsonMatch[0];
        }

        // 最终清理
        cleanText = cleanText.trim();

        console.log('清理后的JSON文本:', cleanText);
        jsonData = JSON.parse(cleanText);
      } catch (parseError) {
        console.error('JSON解析失败:', text);
        console.error('解析错误:', parseError);
        throw new Error('AI响应格式错误，无法解析结果');
      }

      // 验证数据格式
      if (!jsonData.foods || !Array.isArray(jsonData.foods)) {
        throw new Error('AI响应格式错误：foods字段缺失或格式错误');
      }

      // 如果没有识别到食物，返回空结果（这是正常情况，比如非食物图片）
      if (jsonData.foods.length === 0) {
        return {
          foods: [],
          rawResponse: response
        };
      }

      // 验证每个食物条目
      const validatedFoods = jsonData.foods.map((food: any, index: number) => {
        if (!food.name || typeof food.name !== 'string') {
          throw new Error(`食物${index + 1}：名称缺失或格式错误`);
        }
        
        if (typeof food.calories !== 'number' || food.calories < 0) {
          throw new Error(`食物${index + 1}：卡路里数值错误`);
        }
        
        if (typeof food.weight !== 'number' || food.weight <= 0) {
          throw new Error(`食物${index + 1}：重量数值错误`);
        }
        
        if (typeof food.confidence !== 'number' || food.confidence < 0 || food.confidence > 1) {
          throw new Error(`食物${index + 1}：置信度数值错误`);
        }

        return {
          name: food.name.trim(),
          calories: Math.round(food.calories),
          weight: Math.round(food.weight),
          confidence: Math.round(food.confidence * 100) / 100,
          alternatives: Array.isArray(food.alternatives) ? food.alternatives : [],
          dataSource: food.dataSource || (method === 'text' ? 'text_analysis' : 'visual_estimation'),
          nutrition: food.nutrition || {
            protein: Math.round(food.calories * 0.15 / 4),
            fat: Math.round(food.calories * 0.25 / 9),
            carbs: Math.round(food.calories * 0.6 / 4),
            fiber: Math.round(food.calories * 0.05 / 4),
            sugar: Math.round(food.calories * 0.1 / 4),
            sodium: Math.round(food.calories * 0.5)
          },
          labelInfo: food.labelInfo || {
            hasLabel: false,
            servingSize: null,
            labelAccuracy: null
          }
        };
      });

      return {
        foods: validatedFoods,
        rawResponse: response
      };
    } catch (error) {
      console.error('解析API响应失败:', error);
      throw new Error(error instanceof Error ? error.message : '解析AI响应失败');
    }
  }

  /**
   * 将文件转换为base64（移除data URL前缀，符合Gemini API要求）
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = () => {
        if (typeof reader.result === 'string') {
          // 移除data URL前缀 (data:image/...;base64,)，只保留纯净的base64数据
          const base64Data = reader.result.split(',')[1];
          if (base64Data) {
            resolve(base64Data);
          } else {
            reject(new Error('Base64数据提取失败'));
          }
        } else {
          reject(new Error('文件读取失败'));
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      reader.readAsDataURL(file);
    });
  }

  /**
   * 检查API配置
   */
  isConfigured(): boolean {
    return !!this.apiKey && !!this.endpoint;
  }

  /**
   * 获取配置信息
   */
  getConfig() {
    return {
      hasApiKey: !!this.apiKey,
      endpoint: this.endpoint,
      model: this.model,
      timeout: this.timeout
    };
  }
}

// 创建全局实例
export const geminiService = new GeminiService();