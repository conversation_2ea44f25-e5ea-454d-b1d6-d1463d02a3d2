import React, { useRef, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Badge } from '@/shared/components';
import { DailySummary } from '@/shared/types';
import { formatPercentage, cn } from '@/shared/utils';
import { NumberDisplay, CalorieDisplay } from '@/shared/components/atoms';
import { useAccessibleAnimation } from '@/shared/hooks/useAnimation';

interface NutritionCardProps {
  summary: DailySummary;
  showDetails?: boolean;
}

const NutritionCard: React.FC<NutritionCardProps> = ({ 
  summary, 
  showDetails = true 
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const { createAccessibleAnimation } = useAccessibleAnimation();
  const getStatusText = (status: string) => {
    switch (status) {
      case 'under':
        return '未达标';
      case 'normal':
        return '达标';
      case 'over':
        return '接近超标';
      case 'exceed':
        return '超标';
      default:
        return '无数据';
    }
  };

  const getProgressBarColor = (percentage: number) => {
    if (percentage < 90) return 'bg-blue-500';
    if (percentage <= 110) return 'bg-green-500';
    if (percentage <= 130) return 'bg-yellow-500';
    return 'bg-red-500';
  };
  
  // Animate progress bar on mount and value changes
  useEffect(() => {
    const progressBar = progressBarRef.current;
    if (!progressBar) return;
    
    createAccessibleAnimation(progressBar, {
      width: ['0%', `${Math.min(summary.percentage, 100)}%`],
      duration: 1200,
      easing: 'easeOutQuart',
      delay: 300
    });
  }, [summary.percentage, createAccessibleAnimation]);
  
  // Card hover animation
  useEffect(() => {
    const card = cardRef.current;
    if (!card) return;
    
    const handleMouseEnter = () => {
      createAccessibleAnimation(card, {
        translateY: [0, -2],
        boxShadow: [
          '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
          '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
        ],
        duration: 200,
        easing: 'easeOutQuart'
      });
    };
    
    const handleMouseLeave = () => {
      createAccessibleAnimation(card, {
        translateY: [-2, 0],
        boxShadow: [
          '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
        ],
        duration: 200,
        easing: 'easeOutQuart'
      });
    };
    
    card.addEventListener('mouseenter', handleMouseEnter);
    card.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      card.removeEventListener('mouseenter', handleMouseEnter);
      card.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [createAccessibleAnimation]);

  return (
    <Card 
      ref={cardRef}
      className="transition-all duration-200 hover:transform-gpu cursor-pointer"
    >
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>今日营养摄入</CardTitle>
          <Badge 
            variant={summary.status === 'normal' ? 'success' : 
                    summary.status === 'over' ? 'warning' : 
                    summary.status === 'exceed' ? 'danger' : 'default'}
          >
            {getStatusText(summary.status)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 卡路里总览 */}
          <div className="text-center transition-all duration-200">
            <div className="text-3xl font-bold text-gray-900 mb-1 transition-colors duration-200">
              <CalorieDisplay value={summary.totalCalories} />
            </div>
            <div className="text-sm text-gray-500 transition-colors duration-200">
              目标：<CalorieDisplay value={summary.calorieLimit} />
            </div>
            <div className="text-sm text-gray-500 transition-colors duration-200">
              剩余：<CalorieDisplay value={Math.max(0, summary.remainingCalories)} />
            </div>
          </div>

          {/* 进度条 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">完成度</span>
              <span className="font-medium">{formatPercentage(summary.percentage / 100)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div
                ref={progressBarRef}
                className={cn(
                  'h-3 rounded-full transition-all duration-300 transform-gpu',
                  getProgressBarColor(summary.percentage)
                )}
                style={{ 
                  width: `${Math.min(summary.percentage, 100)}%`,
                  boxShadow: 'inset 0 1px 2px rgba(0, 0, 0, 0.1)'
                }}
              />
            </div>
          </div>

          {/* 三餐分布 */}
          {showDetails && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">三餐分布</h4>
              <div className="space-y-2">
                {Object.entries(summary.mealBreakdown).map(([mealType, meal]) => {
                  if (mealType === 'snack' && meal.calories === 0) return null;
                  
                  const mealNames = {
                    breakfast: '早餐',
                    lunch: '午餐',
                    dinner: '晚餐',
                    snack: '零食'
                  };

                  return (
                    <div 
                      key={mealType} 
                      className="flex items-center justify-between p-2 rounded-lg transition-all duration-200 hover:bg-gray-50 hover:transform-gpu hover:scale-[1.01]"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">
                          {mealNames[mealType as keyof typeof mealNames]}
                        </span>
                        <Badge size="sm" variant="default">
                          {meal.foodCount}项
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">
                          <CalorieDisplay value={meal.calories} />
                        </span>
                        <span className="text-xs text-gray-500">
                          / <CalorieDisplay value={meal.calorieLimit} />
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* 营养素分布 */}
          {showDetails && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">营养素摄入</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-50 rounded-lg p-3 text-center transition-all duration-200 hover:bg-gray-100 hover:transform-gpu hover:scale-105 cursor-pointer">
                  <div className="text-lg font-semibold text-gray-900 transition-colors duration-200">
                    <NumberDisplay value={summary.nutrition.protein} unit="g" />
                  </div>
                  <div className="text-xs text-gray-500 transition-colors duration-200">蛋白质</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 text-center transition-all duration-200 hover:bg-gray-100 hover:transform-gpu hover:scale-105 cursor-pointer">
                  <div className="text-lg font-semibold text-gray-900 transition-colors duration-200">
                    <NumberDisplay value={summary.nutrition.carbs} unit="g" />
                  </div>
                  <div className="text-xs text-gray-500 transition-colors duration-200">碳水化合物</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 text-center transition-all duration-200 hover:bg-gray-100 hover:transform-gpu hover:scale-105 cursor-pointer">
                  <div className="text-lg font-semibold text-gray-900 transition-colors duration-200">
                    <NumberDisplay value={summary.nutrition.fat} unit="g" />
                  </div>
                  <div className="text-xs text-gray-500 transition-colors duration-200">脂肪</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 text-center transition-all duration-200 hover:bg-gray-100 hover:transform-gpu hover:scale-105 cursor-pointer">
                  <div className="text-lg font-semibold text-gray-900 transition-colors duration-200">
                    <NumberDisplay value={summary.nutrition.fiber} unit="g" />
                  </div>
                  <div className="text-xs text-gray-500 transition-colors duration-200">膳食纤维</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default NutritionCard;