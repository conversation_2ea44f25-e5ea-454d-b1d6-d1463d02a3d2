import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { DailySummary, NutritionAnalysis, MealSummary, FoodRecord, DailyFoodRecords, MealType } from '@/shared/types';
import { formatDate, getDateRange } from '@/shared/utils';

interface NutritionState {
  // 状态
  dailySummaries: Record<string, DailySummary>; // 以日期为key的每日汇总
  dailyFoodRecords: Record<string, DailyFoodRecords>; // 以日期为key的每日食物记录
  currentDate: Date;
  loading: boolean;
  error: string | null;

  // 操作
  setCurrentDate: (date: Date) => void;
  getDailySummary: (date: Date) => DailySummary | null;
  updateDailySummary: (date: Date, summary: Partial<DailySummary>) => void;
  addFoodRecord: (date: Date, mealType: MealType, calories: number, calorieLimit: number) => void;
  calculateMealSummary: (date: Date, mealType: string, calories: number, calorieLimit: number) => MealSummary;
  getWeeklyAnalysis: (startDate: Date) => NutritionAnalysis;
  getMonthlyAnalysis: (year: number, month: number) => NutritionAnalysis;
  clearData: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // 食物记录操作
  addDetailedFoodRecord: (date: Date, foodRecord: Omit<FoodRecord, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateFoodRecord: (date: Date, recordId: string, updates: Partial<FoodRecord>) => void;
  deleteFoodRecord: (date: Date, recordId: string) => void;
  getDailyFoodRecords: (date: Date) => DailyFoodRecords | null;
  getFoodRecordsByMeal: (date: Date, mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack') => FoodRecord[];
  updateNutritionData: (date: Date, nutrition: { protein: number; fat: number; carbs: number; fiber: number; sugar: number; sodium: number; }) => void;
}

export const useNutritionStore = create<NutritionState>()(
  persist(
    (set, get) => ({
      // 初始状态
      dailySummaries: {},
      dailyFoodRecords: {},
      currentDate: new Date(),
      loading: false,
      error: null,

      // 设置当前日期
      setCurrentDate: (date: Date) => {
        set({ currentDate: date });
      },

      // 获取每日汇总
      getDailySummary: (date: Date) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        return get().dailySummaries[dateKey] || null;
      },

      // 更新每日汇总
      updateDailySummary: (date: Date, summary: Partial<DailySummary>) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        const currentSummaries = get().dailySummaries;
        
        const existingSummary = currentSummaries[dateKey];
        const defaultSummary: DailySummary = {
          id: `summary_${Date.now()}`,
          createdAt: new Date(),
          updatedAt: new Date(),
          date,
          totalCalories: 0,
          calorieLimit: 2000,
          remainingCalories: 2000,
          mealBreakdown: {
            breakfast: { mealType: 'breakfast', calories: 0, calorieLimit: 600, foodCount: 0, percentage: 0 },
            lunch: { mealType: 'lunch', calories: 0, calorieLimit: 800, foodCount: 0, percentage: 0 },
            dinner: { mealType: 'dinner', calories: 0, calorieLimit: 600, foodCount: 0, percentage: 0 },
            snack: { mealType: 'snack', calories: 0, calorieLimit: 0, foodCount: 0, percentage: 0 }
          },
          nutrition: {
            protein: 0,
            fat: 0,
            carbs: 0,
            fiber: 0,
            sugar: 0,
            sodium: 0
          },
          status: 'under',
          percentage: 0
        };

        const updatedSummary: DailySummary = {
          ...defaultSummary,
          ...existingSummary,
          ...summary,
          updatedAt: new Date()
        };

        // 重新计算状态和百分比
        const percentage = (updatedSummary.totalCalories / updatedSummary.calorieLimit) * 100;
        updatedSummary.percentage = percentage;
        updatedSummary.remainingCalories = updatedSummary.calorieLimit - updatedSummary.totalCalories;

        if (percentage < 90) {
          updatedSummary.status = 'under';
        } else if (percentage <= 110) {
          updatedSummary.status = 'normal';
        } else if (percentage <= 130) {
          updatedSummary.status = 'over';
        } else {
          updatedSummary.status = 'exceed';
        }

        set({
          dailySummaries: {
            ...currentSummaries,
            [dateKey]: updatedSummary
          }
        });
      },

      // 添加食物记录
      addFoodRecord: (date: Date, mealType: MealType, calories: number, calorieLimit: number) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        const currentSummaries = get().dailySummaries;
        const existingSummary = currentSummaries[dateKey];

        // 创建默认汇总（如果不存在）
        const defaultSummary: DailySummary = {
          id: `summary_${dateKey}_${Date.now()}`,
          createdAt: new Date(),
          updatedAt: new Date(),
          date: new Date(dateKey),
          totalCalories: 0,
          calorieLimit,
          remainingCalories: calorieLimit,
          mealBreakdown: {
            breakfast: { mealType: 'breakfast', calories: 0, calorieLimit: calorieLimit * 0.3, foodCount: 0, percentage: 0 },
            lunch: { mealType: 'lunch', calories: 0, calorieLimit: calorieLimit * 0.4, foodCount: 0, percentage: 0 },
            dinner: { mealType: 'dinner', calories: 0, calorieLimit: calorieLimit * 0.3, foodCount: 0, percentage: 0 },
            snack: { mealType: 'snack', calories: 0, calorieLimit: 0, foodCount: 0, percentage: 0 }
          },
          nutrition: {
            protein: 0,
            fat: 0,
            carbs: 0,
            fiber: 0,
            sugar: 0,
            sodium: 0
          },
          status: 'under',
          percentage: 0
        };

        const currentSummary = existingSummary || defaultSummary;

        // 更新餐次数据
        const updatedMealBreakdown = { ...currentSummary.mealBreakdown };
        updatedMealBreakdown[mealType] = {
          ...updatedMealBreakdown[mealType],
          calories: updatedMealBreakdown[mealType].calories + calories,
          foodCount: updatedMealBreakdown[mealType].foodCount + 1
        };

        // 计算餐次百分比
        updatedMealBreakdown[mealType].percentage =
          (updatedMealBreakdown[mealType].calories / updatedMealBreakdown[mealType].calorieLimit) * 100;

        // 计算总卡路里
        const newTotalCalories = currentSummary.totalCalories + calories;
        const percentage = (newTotalCalories / calorieLimit) * 100;

        const updatedSummary: DailySummary = {
          ...currentSummary,
          totalCalories: newTotalCalories,
          remainingCalories: calorieLimit - newTotalCalories,
          mealBreakdown: updatedMealBreakdown,
          percentage,
          status: percentage < 90 ? 'under' : percentage <= 110 ? 'normal' : 'exceed',
          updatedAt: new Date()
        };

        set({
          dailySummaries: {
            ...currentSummaries,
            [dateKey]: updatedSummary
          }
        });
      },

      // 计算单餐汇总
      calculateMealSummary: (date: Date, mealType: string, calories: number, calorieLimit: number) => {
        const percentage = calorieLimit > 0 ? (calories / calorieLimit) * 100 : 0;
        
        return {
          mealType: mealType as any,
          calories,
          calorieLimit,
          foodCount: 1, // 这里简化处理，实际应该从食物条目计算
          percentage
        };
      },

      // 获取周度分析
      getWeeklyAnalysis: (startDate: Date) => {
        const { start, end } = getDateRange('week', startDate);
        const summaries = get().dailySummaries;
        
        const weekData: { date: Date; calories: number }[] = [];
        let totalCalories = 0;
        let totalTarget = 0;
        let daysWithData = 0;

        // 遍历一周的数据
        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
          const dateKey = formatDate(d, 'yyyy-MM-dd');
          const summary = summaries[dateKey];
          
          if (summary) {
            weekData.push({
              date: new Date(d),
              calories: summary.totalCalories
            });
            totalCalories += summary.totalCalories;
            totalTarget += summary.calorieLimit;
            daysWithData++;
          } else {
            weekData.push({
              date: new Date(d),
              calories: 0
            });
          }
        }

        const averageCalories = daysWithData > 0 ? totalCalories / daysWithData : 0;
        const averageTarget = daysWithData > 0 ? totalTarget / daysWithData : 0;
        const adherenceRate = averageTarget > 0 ? (averageCalories / averageTarget) : 0;

        return {
          period: 'weekly',
          startDate: start,
          endDate: end,
          calories: {
            average: averageCalories,
            total: totalCalories,
            target: averageTarget,
            adherenceRate
          },
          nutrition: {
            protein: { average: 0, total: 0, recommended: 0, percentage: 0 },
            fat: { average: 0, total: 0, recommended: 0, percentage: 0 },
            carbs: { average: 0, total: 0, recommended: 0, percentage: 0 },
            fiber: { average: 0, total: 0, recommended: 0, percentage: 0 }
          },
          trends: weekData
        };
      },

      // 获取月度分析
      getMonthlyAnalysis: (year: number, month: number) => {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0);
        const summaries = get().dailySummaries;
        
        const monthData: { date: Date; calories: number }[] = [];
        let totalCalories = 0;
        let totalTarget = 0;
        let daysWithData = 0;

        // 遍历一个月的数据
        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
          const dateKey = formatDate(d, 'yyyy-MM-dd');
          const summary = summaries[dateKey];
          
          if (summary) {
            monthData.push({
              date: new Date(d),
              calories: summary.totalCalories
            });
            totalCalories += summary.totalCalories;
            totalTarget += summary.calorieLimit;
            daysWithData++;
          } else {
            monthData.push({
              date: new Date(d),
              calories: 0
            });
          }
        }

        const averageCalories = daysWithData > 0 ? totalCalories / daysWithData : 0;
        const averageTarget = daysWithData > 0 ? totalTarget / daysWithData : 0;
        const adherenceRate = averageTarget > 0 ? (averageCalories / averageTarget) : 0;

        return {
          period: 'monthly',
          startDate,
          endDate,
          calories: {
            average: averageCalories,
            total: totalCalories,
            target: averageTarget,
            adherenceRate
          },
          nutrition: {
            protein: { average: 0, total: 0, recommended: 0, percentage: 0 },
            fat: { average: 0, total: 0, recommended: 0, percentage: 0 },
            carbs: { average: 0, total: 0, recommended: 0, percentage: 0 },
            fiber: { average: 0, total: 0, recommended: 0, percentage: 0 }
          },
          trends: monthData
        };
      },

      // 清除数据
      clearData: () => {
        set({
          dailySummaries: {},
          error: null
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error });
      },

      // 添加详细食物记录
      addDetailedFoodRecord: (date: Date, foodRecord: Omit<FoodRecord, 'id' | 'createdAt' | 'updatedAt'>) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        const currentRecords = get().dailyFoodRecords;

        // 生成唯一ID
        const id = `food_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const now = new Date();

        const newRecord: FoodRecord = {
          ...foodRecord,
          id,
          createdAt: now,
          updatedAt: now
        };

        // 获取或创建当日记录
        const existingDayRecords = currentRecords[dateKey] || {
          date: dateKey,
          records: [],
          mealRecords: {
            breakfast: [],
            lunch: [],
            dinner: [],
            snack: []
          }
        };

        // 添加到记录列表
        const updatedRecords = [...existingDayRecords.records, newRecord];

        // 按餐次分组
        const mealRecords = {
          breakfast: updatedRecords.filter(r => r.mealType === 'breakfast'),
          lunch: updatedRecords.filter(r => r.mealType === 'lunch'),
          dinner: updatedRecords.filter(r => r.mealType === 'dinner'),
          snack: updatedRecords.filter(r => r.mealType === 'snack')
        };

        const updatedDayRecords: DailyFoodRecords = {
          date: dateKey,
          records: updatedRecords,
          mealRecords
        };

        // 更新存储
        set({
          dailyFoodRecords: {
            ...currentRecords,
            [dateKey]: updatedDayRecords
          }
        });

        // 同时更新营养汇总（确保calories有效）
        const calories = foodRecord.calories || 0;
        if (calories > 0) {
          // 获取用户的卡路里目标，默认2000
          const calorieLimit = 2000; // 可以从用户配置中获取
          get().addFoodRecord(date, foodRecord.mealType, calories, calorieLimit);

          // 更新详细营养数据
          get().updateNutritionData(date, foodRecord.nutrition);
        }
      },

      // 获取每日食物记录
      getDailyFoodRecords: (date: Date) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        return get().dailyFoodRecords[dateKey] || null;
      },

      // 按餐次获取食物记录
      getFoodRecordsByMeal: (date: Date, mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack') => {
        const dayRecords = get().getDailyFoodRecords(date);
        return dayRecords?.mealRecords[mealType] || [];
      },

      // 更新食物记录
      updateFoodRecord: (date: Date, recordId: string, updates: Partial<FoodRecord>) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        const currentRecords = get().dailyFoodRecords;
        const dayRecords = currentRecords[dateKey];

        if (!dayRecords) return;

        // 找到原始记录以计算差值
        const originalRecord = dayRecords.records.find(r => r.id === recordId);
        if (!originalRecord) return;

        // 更新记录
        const updatedRecords = dayRecords.records.map(record =>
          record.id === recordId
            ? { ...record, ...updates, updatedAt: new Date(), isEdited: true }
            : record
        );

        // 找到更新后的记录
        const updatedRecord = updatedRecords.find(r => r.id === recordId);
        if (!updatedRecord) return;

        // 重新按餐次分组
        const mealRecords = {
          breakfast: updatedRecords.filter(r => r.mealType === 'breakfast'),
          lunch: updatedRecords.filter(r => r.mealType === 'lunch'),
          dinner: updatedRecords.filter(r => r.mealType === 'dinner'),
          snack: updatedRecords.filter(r => r.mealType === 'snack')
        };

        const updatedDayRecords: DailyFoodRecords = {
          date: dateKey,
          records: updatedRecords,
          mealRecords
        };

        // 更新存储
        set({
          dailyFoodRecords: {
            ...currentRecords,
            [dateKey]: updatedDayRecords
          }
        });

        // 更新营养汇总数据
        const calorieDiff = updatedRecord.calories - originalRecord.calories;
        if (calorieDiff !== 0) {
          get().addFoodRecord(date, updatedRecord.mealType, calorieDiff, 2000);
        }

        // 更新详细营养数据
        const nutritionDiff = {
          protein: updatedRecord.nutrition.protein - originalRecord.nutrition.protein,
          fat: updatedRecord.nutrition.fat - originalRecord.nutrition.fat,
          carbs: updatedRecord.nutrition.carbs - originalRecord.nutrition.carbs,
          fiber: updatedRecord.nutrition.fiber - originalRecord.nutrition.fiber,
          sugar: updatedRecord.nutrition.sugar - originalRecord.nutrition.sugar,
          sodium: updatedRecord.nutrition.sodium - originalRecord.nutrition.sodium
        };

        // 只有当营养数据有变化时才更新
        const hasNutritionChange = Object.values(nutritionDiff).some(value => value !== 0);
        if (hasNutritionChange) {
          get().updateNutritionData(date, nutritionDiff);
        }
      },

      // 删除食物记录
      deleteFoodRecord: (date: Date, recordId: string) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        const currentRecords = get().dailyFoodRecords;
        const dayRecords = currentRecords[dateKey];

        if (!dayRecords) return;

        // 找到要删除的记录
        const recordToDelete = dayRecords.records.find(r => r.id === recordId);
        if (!recordToDelete) return;

        // 过滤掉要删除的记录
        const updatedRecords = dayRecords.records.filter(record => record.id !== recordId);

        // 重新按餐次分组
        const mealRecords = {
          breakfast: updatedRecords.filter(r => r.mealType === 'breakfast'),
          lunch: updatedRecords.filter(r => r.mealType === 'lunch'),
          dinner: updatedRecords.filter(r => r.mealType === 'dinner'),
          snack: updatedRecords.filter(r => r.mealType === 'snack')
        };

        const updatedDayRecords: DailyFoodRecords = {
          date: dateKey,
          records: updatedRecords,
          mealRecords
        };

        // 更新存储
        set({
          dailyFoodRecords: {
            ...currentRecords,
            [dateKey]: updatedDayRecords
          }
        });

        // 同时更新营养汇总（减去删除的卡路里）
        get().addFoodRecord(date, recordToDelete.mealType, -recordToDelete.calories, 2000);

        // 减去删除的营养数据
        const negativeNutrition = {
          protein: -recordToDelete.nutrition.protein,
          fat: -recordToDelete.nutrition.fat,
          carbs: -recordToDelete.nutrition.carbs,
          fiber: -recordToDelete.nutrition.fiber,
          sugar: -recordToDelete.nutrition.sugar,
          sodium: -recordToDelete.nutrition.sodium
        };
        get().updateNutritionData(date, negativeNutrition);
      },

      // 更新营养数据
      updateNutritionData: (date: Date, nutrition: { protein: number; fat: number; carbs: number; fiber: number; sugar: number; sodium: number; }) => {
        const dateKey = formatDate(date, 'yyyy-MM-dd');
        const currentSummaries = get().dailySummaries;
        const existingSummary = currentSummaries[dateKey];

        if (!existingSummary) return;

        const updatedNutrition = {
          protein: Math.max(0, existingSummary.nutrition.protein + nutrition.protein),
          fat: Math.max(0, existingSummary.nutrition.fat + nutrition.fat),
          carbs: Math.max(0, existingSummary.nutrition.carbs + nutrition.carbs),
          fiber: Math.max(0, existingSummary.nutrition.fiber + nutrition.fiber),
          sugar: Math.max(0, existingSummary.nutrition.sugar + nutrition.sugar),
          sodium: Math.max(0, existingSummary.nutrition.sodium + nutrition.sodium)
        };

        const updatedSummary: DailySummary = {
          ...existingSummary,
          nutrition: updatedNutrition,
          updatedAt: new Date()
        };

        set({
          dailySummaries: {
            ...currentSummaries,
            [dateKey]: updatedSummary
          }
        });
      }
    }),
    {
      name: 'nutrition-data-storage',
      partialize: (state) => ({
        dailySummaries: state.dailySummaries,
        dailyFoodRecords: state.dailyFoodRecords
      })
    }
  )
);