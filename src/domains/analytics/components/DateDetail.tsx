import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, Card<PERSON>ontent, Badge } from '@/shared/components';
import { MealDistributionChart, NutrientBreakdownChart } from './Charts';
import { DailySummary } from '@/shared/types';
import { formatDate } from '@/shared/utils';
import { CalorieDisplay } from '@/shared/components/atoms';
import { formatNutritionData } from '@/shared/utils/format';

interface DateDetailProps {
  date: Date;
  summary: DailySummary | null;
  onClose?: () => void;
}

const DateDetail: React.FC<DateDetailProps> = ({ date, summary, onClose }) => {
  if (!summary) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{formatDate(date, 'yyyy年MM月dd日')}</CardTitle>
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <p>这一天还没有记录任何食物</p>
            <p className="text-sm mt-1">开始记录您的第一餐吧！</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'under':
        return <Badge variant="default">未达标</Badge>;
      case 'normal':
        return <Badge variant="success">达标</Badge>;
      case 'over':
        return <Badge variant="warning">接近超标</Badge>;
      case 'exceed':
        return <Badge variant="danger">超标</Badge>;
      default:
        return <Badge variant="default">无数据</Badge>;
    }
  };

  const mealNames = {
    breakfast: '早餐',
    lunch: '午餐',
    dinner: '晚餐',
    snack: '零食'
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{formatDate(date, 'yyyy年MM月dd日')}</CardTitle>
            <div className="flex items-center gap-2 mt-1">
              {getStatusBadge(summary.status)}
              <span className="text-sm text-gray-500">
                完成度 {Math.round(summary.percentage)}%
              </span>
            </div>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl"
            >
              ✕
            </button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 卡路里概览 */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-blue-700">
                <CalorieDisplay value={summary.totalCalories} />
              </div>
              <div className="text-xs text-blue-600">已摄入</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-700">
                <CalorieDisplay value={summary.calorieLimit} />
              </div>
              <div className="text-xs text-gray-600">目标</div>
            </div>
            <div className="bg-green-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-green-700">
                <CalorieDisplay value={Math.max(0, summary.remainingCalories)} />
              </div>
              <div className="text-xs text-green-600">剩余</div>
            </div>
          </div>

          {/* 三餐分布 */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">三餐分布</h4>
            <div className="flex items-center gap-4">
              <MealDistributionChart
                breakfast={summary.mealBreakdown.breakfast.calories}
                lunch={summary.mealBreakdown.lunch.calories}
                dinner={summary.mealBreakdown.dinner.calories}
                snack={summary.mealBreakdown.snack.calories}
                size={150}
              />
              <div className="flex-1 space-y-2">
                {Object.entries(summary.mealBreakdown).map(([mealType, meal]) => {
                  if (mealType === 'snack' && meal.calories === 0) return null;
                  
                  return (
                    <div key={mealType} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <span className="text-gray-600">
                          {mealNames[mealType as keyof typeof mealNames]}
                        </span>
                        <Badge size="sm" variant="default">
                          {meal.foodCount}项
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          <CalorieDisplay value={meal.calories} />
                        </div>
                        <div className="text-xs text-gray-500">
                          目标: <CalorieDisplay value={meal.calorieLimit} />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* 营养素分布 */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">营养素分布</h4>
            <div className="flex items-center gap-4">
              <NutrientBreakdownChart
                protein={summary.nutrition.protein}
                fat={summary.nutrition.fat}
                carbs={summary.nutrition.carbs}
                size={130}
              />
              <div className="flex-1 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">蛋白质</span>
                  <span className="font-medium">{formatNutritionData(summary.nutrition.protein, 'g')}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">脂肪</span>
                  <span className="font-medium">{formatNutritionData(summary.nutrition.fat, 'g')}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">碳水化合物</span>
                  <span className="font-medium">{formatNutritionData(summary.nutrition.carbs, 'g')}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">膳食纤维</span>
                  <span className="font-medium">{formatNutritionData(summary.nutrition.fiber, 'g')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 进度条 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">今日进度</span>
              <span className="font-medium">{Math.round(summary.percentage)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  summary.percentage < 90 ? 'bg-blue-500' :
                  summary.percentage <= 110 ? 'bg-green-500' :
                  summary.percentage <= 130 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${Math.min(summary.percentage, 100)}%` }}
              />
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <button className="flex-1 bg-primary-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors">
              添加食物
            </button>
            <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
              查看详情
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DateDetail;