import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>H<PERSON><PERSON>, CardTitle, CardContent, <PERSON><PERSON>, Badge, Spinner } from '@/shared/components';
import { geminiService } from '@/infrastructure/ai';
import { FoodRecognitionResult } from '@/shared/types';
import { CalorieDisplay, WeightDisplay } from '@/shared/components/atoms';
import { 
  SparklesIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface FoodRecognitionProps {
  onFoodSelect?: (food: {
    name: string;
    calories: number;
    weight: number;
    confidence: number;
  }) => void;
  className?: string;
}

const FoodRecognition: React.FC<FoodRecognitionProps> = ({
  onFoodSelect,
  className
}) => {
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [recognitionResult, setRecognitionResult] = useState<FoodRecognitionResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 处理图片选择
  const handleImageSelect = (file: File, thumbnail?: string) => {
    setSelectedImage(file);
    setImagePreview(thumbnail || URL.createObjectURL(file));
    setRecognitionResult(null);
    setError(null);
  };

  // 开始识别
  const startRecognition = async () => {
    if (!selectedImage) return;

    try {
      setIsRecognizing(true);
      setError(null);

      // 检查API配置
      if (!geminiService.isConfigured()) {
        throw new Error('AI服务未配置，请检查API密钥设置');
      }

      // 调用AI识别
      const result = await geminiService.recognizeFood(selectedImage);
      setRecognitionResult(result);

      // 如果没有识别到食物
      if (result.foods.length === 0) {
        setError('未识别到食物，请尝试拍摄更清晰的食物图片');
      }
    } catch (err) {
      console.error('食物识别失败:', err);
      setError(err instanceof Error ? err.message : '识别失败，请重试');
    } finally {
      setIsRecognizing(false);
    }
  };

  // 选择识别结果
  const selectFood = (food: FoodRecognitionResult['foods'][0]) => {
    onFoodSelect?.(food);
  };

  // 重新选择图片
  const resetSelection = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setRecognitionResult(null);
    setError(null);
  };

  // 获取置信度颜色
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  // 获取置信度文本
  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.8) return '高置信度';
    if (confidence >= 0.6) return '中等置信度';
    return '低置信度';
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SparklesIcon className="h-5 w-5 text-primary-600" />
            AI食物识别
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 图片选择 */}
            {!selectedImage ? (
              <ImagePicker
                onImageSelect={handleImageSelect}
                placeholder="拍照或选择食物图片进行AI识别"
                maxSizeMB={2}
              />
            ) : (
              <div className="space-y-4">
                {/* 图片预览 */}
                <div className="relative">
                  <img
                    src={imagePreview!}
                    alt="选择的食物图片"
                    className="w-full h-48 object-cover rounded-lg border border-gray-200"
                  />
                  <button
                    onClick={resetSelection}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* 识别按钮 */}
                {!recognitionResult && !isRecognizing && (
                  <Button
                    variant="primary"
                    onClick={startRecognition}
                    disabled={isRecognizing}
                    fullWidth
                    className="flex items-center justify-center gap-2"
                  >
                    <SparklesIcon className="h-5 w-5" />
                    开始AI识别
                  </Button>
                )}

                {/* 识别中状态 */}
                {isRecognizing && (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                      <Spinner size="lg" color="primary" />
                      <p className="mt-3 text-sm text-gray-600">
                        AI正在分析图片中的食物...
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        这可能需要几秒钟时间
                      </p>
                    </div>
                  </div>
                )}

                {/* 错误提示 */}
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-red-800">识别失败</h4>
                        <p className="text-sm text-red-700 mt-1">{error}</p>
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={startRecognition}
                          className="mt-2"
                        >
                          重试
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* 识别结果 */}
                {recognitionResult && recognitionResult.foods.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      <h4 className="font-medium text-gray-900">
                        识别到 {recognitionResult.foods.length} 种食物
                      </h4>
                    </div>

                    <div className="space-y-3">
                      {recognitionResult.foods.map((food, index) => (
                        <div
                          key={index}
                          className="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h5 className="font-medium text-gray-900">{food.name}</h5>
                                <Badge
                                  size="sm"
                                  className={getConfidenceColor(food.confidence)}
                                >
                                  {getConfidenceText(food.confidence)}
                                </Badge>
                              </div>
                              
                              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                <div>
                                  <span className="font-medium">卡路里：</span>
                                  <CalorieDisplay value={food.calories} />
                                </div>
                                <div>
                                  <span className="font-medium">重量：</span>
                                  <WeightDisplay value={food.weight} />
                                </div>
                              </div>

                              {food.alternatives && food.alternatives.length > 0 && (
                                <div className="mt-2">
                                  <p className="text-xs text-gray-500">
                                    备选识别：{food.alternatives.join('、')}
                                  </p>
                                </div>
                              )}
                            </div>

                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => selectFood(food)}
                              className="ml-3"
                            >
                              选择
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    <Button
                      variant="secondary"
                      onClick={resetSelection}
                      fullWidth
                    >
                      重新选择图片
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* API配置提示 */}
            {!geminiService.isConfigured() && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800">AI服务未配置</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      请在环境变量中配置 VITE_GEMINI_API_KEY 以启用AI食物识别功能
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FoodRecognition;