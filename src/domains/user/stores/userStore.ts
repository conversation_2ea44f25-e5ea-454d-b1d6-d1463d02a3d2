import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UserProfile, CreateUserProfileForm, UpdateUserProfileForm } from '@/shared/types';
import { calculateNutritionPlan } from '@/shared/utils';
import { userRepository } from '@/infrastructure/storage';

interface UserState {
  // 状态
  profile: UserProfile | null;
  isProfileComplete: boolean;
  loading: boolean;
  error: string | null;

  // 操作
  createProfile: (data: CreateUserProfileForm) => Promise<void>;
  updateProfile: (data: UpdateUserProfileForm) => Promise<void>;
  clearProfile: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      // 初始状态
      profile: null,
      isProfileComplete: false,
      loading: false,
      error: null,

      // 创建用户档案
      createProfile: async (data: CreateUserProfileForm) => {
        try {
          set({ loading: true, error: null });

          // 计算BMR和营养计划
          const nutritionPlan = calculateNutritionPlan({
            weight: data.weight,
            height: data.height,
            age: data.age,
            gender: data.gender,
            targetWeight: data.targetWeight,
            targetDays: data.targetDays,
            activityLevel: data.activityLevel
          });

          // 创建完整的用户档案
          const profile: UserProfile = {
            id: `user_${Date.now()}`,
            createdAt: new Date(),
            updatedAt: new Date(),
            
            // 基本信息
            name: data.name,
            height: data.height,
            weight: data.weight,
            age: data.age,
            gender: data.gender,
            
            // 目标设置
            targetWeight: data.targetWeight,
            targetDays: data.targetDays,
            activityLevel: data.activityLevel,
            
            // 计算结果
            bmr: nutritionPlan.bmr,
            tdee: nutritionPlan.tdee,
            dailyCalorieLimit: nutritionPlan.dailyCalorieLimit,
            
            // 默认三餐分配比例
            mealRatios: {
              breakfast: 0.3,
              lunch: 0.4,
              dinner: 0.3
            },
            
            // 默认偏好设置
            preferences: {
              theme: 'system',
              language: 'zh-CN',
              notifications: {
                mealReminders: true,
                dailySummary: true,
                weeklyReport: false
              },
              units: {
                weight: 'kg',
                height: 'cm'
              }
            }
          };

          set({ 
            profile, 
            isProfileComplete: true, 
            loading: false 
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '创建档案失败';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw error;
        }
      },

      // 更新用户档案
      updateProfile: async (data: UpdateUserProfileForm) => {
        try {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('用户档案不存在');
          }

          set({ loading: true, error: null });

          // 如果更新了影响BMR计算的字段，重新计算
          let updatedProfile: UserProfile = { 
            ...currentProfile, 
            ...data, 
            updatedAt: new Date(),
            // 合并preferences，确保保持完整性
            preferences: data.preferences ? { ...currentProfile.preferences, ...data.preferences } : currentProfile.preferences
          };

          const needsRecalculation = 
            data.weight !== undefined ||
            data.height !== undefined ||
            data.age !== undefined ||
            data.gender !== undefined ||
            data.targetWeight !== undefined ||
            data.targetDays !== undefined ||
            data.activityLevel !== undefined;

          if (needsRecalculation) {
            const nutritionPlan = calculateNutritionPlan({
              weight: updatedProfile.weight,
              height: updatedProfile.height,
              age: updatedProfile.age,
              gender: updatedProfile.gender,
              targetWeight: updatedProfile.targetWeight,
              targetDays: updatedProfile.targetDays,
              activityLevel: updatedProfile.activityLevel
            });

            updatedProfile = {
              ...updatedProfile,
              bmr: nutritionPlan.bmr,
              tdee: nutritionPlan.tdee,
              dailyCalorieLimit: nutritionPlan.dailyCalorieLimit
            };
          }

          set({ 
            profile: updatedProfile, 
            loading: false 
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新档案失败';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw error;
        }
      },

      // 清除用户档案
      clearProfile: () => {
        set({ 
          profile: null, 
          isProfileComplete: false, 
          error: null 
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error });
      }
    }),
    {
      name: 'user-profile-storage',
      partialize: (state) => ({ 
        profile: state.profile, 
        isProfileComplete: state.isProfileComplete 
      })
    }
  )
);