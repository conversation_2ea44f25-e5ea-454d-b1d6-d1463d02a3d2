import React, { useState, useRef, useEffect } from 'react';
import { animate } from 'animejs';
import { CreateUserProfileForm, Gender, ActivityLevel, UserProfile } from '@/shared/types';
import { validateWeightLossGoal } from '@/shared/utils';

interface ProfileSetupFormProps {
  onSubmit: (data: CreateUserProfileForm) => Promise<void>;
  loading?: boolean;
  isEditMode?: boolean;
  initialData?: UserProfile | null;
}

const ProfileSetupForm: React.FC<ProfileSetupFormProps> = ({
  onSubmit,
  loading = false,
  isEditMode = false,
  initialData = null
}) => {
  // {{ AURA-X: 完全重新设计 - 清除所有遗留代码，全新DaisyUI布局. Approval: 寸止(ID:1705123500). }}
  const [step, setStep] = useState(1);
  const [data, setData] = useState<Partial<CreateUserProfileForm>>(() => {
    // 如果是编辑模式且有初始数据，则预填充
    if (isEditMode && initialData) {
      return {
        name: initialData.name,
        age: initialData.age,
        gender: initialData.gender,
        height: initialData.height,
        weight: initialData.weight,
        targetWeight: initialData.targetWeight,
        targetDays: initialData.targetDays,
        activityLevel: initialData.activityLevel,
      };
    }
    return {};
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});

  const containerRef = useRef<HTMLDivElement>(null);
  const stepRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});

  // 页面加载动画
  useEffect(() => {
    // 页面加载时的淡入动画
    animate('.step-indicator', {
      opacity: [0, 1],
      translateY: [-20, 0],
      duration: 600,
      ease: 'outQuad'
    });

    animate('.form-container', {
      opacity: [0, 1],
      translateY: [30, 0],
      duration: 800,
      delay: 200,
      ease: 'outQuad'
    });

    animate('.navigation-buttons', {
      opacity: [0, 1],
      translateY: [20, 0],
      duration: 600,
      delay: 400,
      ease: 'outQuad'
    });
  }, []);

  // Anime.js动画函数
  const createStepTransition = (currentSelector: string, nextSelector: string, direction: 'forward' | 'backward') => {
    const translateOut = direction === 'forward' ? -30 : 30;
    const translateIn = direction === 'forward' ? 30 : -30;

    // 当前步骤淡出
    animate(currentSelector, {
      opacity: [1, 0],
      translateX: [0, translateOut],
      duration: 300,
      ease: 'outQuad'
    });

    // 新步骤淡入
    setTimeout(() => {
      animate(nextSelector, {
        opacity: [0, 1],
        translateX: [translateIn, 0],
        duration: 400,
        ease: 'outQuad'
      });
    }, 150);
  };

  const createProgressAnimation = (selector: string, progress: number) => {
    animate(selector, {
      width: `${progress}%`,
      duration: 500,
      ease: 'outQuad'
    });
  };

  // 错误提示动画
  const createErrorAnimation = (element: HTMLElement) => {
    animate(element, {
      translateX: [-10, 10, -10, 10, 0],
      duration: 400,
      ease: 'outQuad'
    });
  };

  // 按钮点击动画
  const createButtonClickAnimation = (element: HTMLElement) => {
    animate(element, {
      scale: [1, 0.95, 1],
      duration: 200,
      ease: 'outQuad'
    });
  };

  // 步骤切换动画
  const animateStepTransition = (newStep: number) => {
    const currentStepEl = stepRefs.current[step];
    const nextStepEl = stepRefs.current[newStep];

    if (currentStepEl && nextStepEl) {
      const direction = newStep > step ? 'forward' : 'backward';
      createStepTransition(
        `[data-step="${step}"]`,
        `[data-step="${newStep}"]`,
        direction
      );
    }
  };

  // 表单更新
  const updateField = (field: keyof CreateUserProfileForm, value: any) => {
    setData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误和警告
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    if (warnings[field]) {
      setWarnings(prev => {
        const newWarnings = { ...prev };
        delete newWarnings[field];
        return newWarnings;
      });
    }
  };

  // 验证步骤
  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!data.height || data.height < 100 || data.height > 250) {
        newErrors.height = '身高需在100-250cm之间';
      }
      // 放宽体重限制：20-500kg
      if (!data.weight || data.weight < 20 || data.weight > 500) {
        newErrors.weight = '体重需在20-500kg之间';
      }
      if (!data.age || data.age < 10 || data.age > 100) {
        newErrors.age = '年龄需在10-100岁之间';
      }
      if (!data.gender) {
        newErrors.gender = '请选择性别';
      }
    }

    if (step === 2) {
      // 放宽目标体重限制：20-500kg
      if (!data.targetWeight || data.targetWeight < 20 || data.targetWeight > 500) {
        newErrors.targetWeight = '目标体重需在20-500kg之间';
      }
      if (!data.targetDays || data.targetDays < 7 || data.targetDays > 365) {
        newErrors.targetDays = '目标天数需在7-365天之间';
      }
      if (!data.activityLevel) {
        newErrors.activityLevel = '请选择活动水平';
      }

      if (data.weight && data.targetWeight && data.targetDays) {
        const validation = validateWeightLossGoal(
          data.weight,
          data.targetWeight,
          data.targetDays,
          data.height,
          data.activityLevel
        );

        // 现在validateWeightLossGoal不会返回isValid: false，所有验证都是警告级别
        if (validation.isWarning && validation.message) {
          setWarnings({ targetWeight: validation.message });
        } else {
          // 清除警告
          setWarnings({});
        }
      }
    }

    setErrors(newErrors);

    // 如果有错误，添加错误动画
    if (Object.keys(newErrors).length > 0) {
      setTimeout(() => {
        Object.keys(newErrors).forEach(field => {
          const errorElement = document.querySelector(`input[value="${data[field as keyof CreateUserProfileForm] || ''}"]`);
          if (errorElement) {
            createErrorAnimation(errorElement as HTMLElement);
          }
        });
      }, 100);
    }

    return Object.keys(newErrors).length === 0;
  };

  // 步骤导航
  const nextStep = () => {
    if (validateCurrentStep()) {
      const newStep = step + 1;
      animateStepTransition(newStep);
      setTimeout(() => setStep(newStep), 200);
    } else {
      // 显示错误动画
      Object.keys(errors).forEach(field => {
        const errorElement = document.querySelector(`[name="${field}"]`) as HTMLElement;
        if (errorElement) {
          createErrorAnimation(errorElement);
        }
      });
    }
  };

  const prevStep = () => {
    const newStep = step - 1;
    animateStepTransition(newStep);
    setTimeout(() => setStep(newStep), 200);
  };

  const handleSubmit = async () => {
    if (validateCurrentStep()) {
      // 成功提交动画
      animate('.submit-button', {
        scale: [1, 1.05, 1],
        backgroundColor: ['#6366f1', '#10b981', '#6366f1'],
        duration: 600,
        ease: 'outQuad'
      });
      await onSubmit(data as CreateUserProfileForm);
    }
  };

  // 动画事件处理
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    createButtonClickAnimation(e.currentTarget);
  };

  const handleInputFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    // 输入框聚焦动画
    animate(e.currentTarget, {
      scale: [1, 1.02, 1],
      duration: 300,
      ease: 'outQuad'
    });
  };

  // 计算推荐体重（基于目标天数和活动水平，但放宽限制）- 集成AURA-X协议
  const calculateRecommendedWeight = () => {
    if (!data.height || !data.weight || !data.targetDays || !data.activityLevel) return;

    // AURA-X协议：基于权威健康指导原则计算推荐值
    // 可以通过context7-mcp获取最新的BMI和健康体重标准

    // 计算BMI和健康体重范围
    const heightInM = data.height / 100;
    const currentBMI = data.weight / (heightInM * heightInM);

    // 健康BMI范围：18.5-24.9
    const healthyMinWeight = 18.5 * heightInM * heightInM;
    const healthyMaxWeight = 24.9 * heightInM * heightInM;

    // 活动水平系数（影响减重潜力）
    const activityMultipliers = {
      sedentary: 0.8,     // 久坐：较低减重潜力
      light: 1.0,         // 轻度活动：标准减重潜力
      moderate: 1.2,      // 中度活动：较好减重潜力
      active: 1.4,        // 高度活动：很好减重潜力
      veryActive: 1.6     // 极高活动：最佳减重潜力
    };

    const activityMultiplier = activityMultipliers[data.activityLevel] || 1.0;

    // 基于目标天数和活动水平计算合理减重量
    const weeksAvailable = data.targetDays / 7;
    const baseWeeklyLoss = 0.5; // 基础每周减重0.5kg
    const adjustedWeeklyLoss = baseWeeklyLoss * activityMultiplier;
    const totalPossibleLoss = weeksAvailable * adjustedWeeklyLoss;

    // 计算推荐目标体重
    let recommendedWeight: number;

    if (currentBMI > 24.9) {
      // 超重：基于活动水平和时间计算合理目标
      const idealReduction = data.weight - healthyMaxWeight;
      const timeBasedReduction = Math.min(totalPossibleLoss, idealReduction);
      recommendedWeight = data.weight - Math.max(timeBasedReduction, 2); // 至少减重2kg
    } else if (currentBMI < 18.5) {
      // 体重不足：推荐增到健康范围
      recommendedWeight = healthyMinWeight;
    } else {
      // 健康范围内：基于活动水平适度优化
      const moderateReduction = Math.min(totalPossibleLoss * 0.7, 5); // 最多减5kg
      recommendedWeight = Math.max(data.weight - moderateReduction, healthyMinWeight);
    }

    // 确保推荐体重在合理范围内
    recommendedWeight = Math.max(recommendedWeight, healthyMinWeight);
    recommendedWeight = Math.min(recommendedWeight, data.weight); // 不超过当前体重

    // 四舍五入到一位小数
    const finalWeight = Math.round(recommendedWeight * 10) / 10;
    updateField('targetWeight', finalWeight);

    // 推荐结果动画
    setTimeout(() => {
      const targetWeightInput = document.querySelector('input[placeholder="60"]') as HTMLElement;
      if (targetWeightInput) {
        animate(targetWeightInput, {
          scale: [1, 1.1, 1],
          backgroundColor: ['#ffffff', '#f0fdf4', '#ffffff'],
          duration: 800,
          ease: 'outQuad'
        });
      }
    }, 100);
  };

  const activityOptions = [
    { value: 'sedentary' as ActivityLevel, label: '久坐不动', desc: '很少或不运动', emoji: '🪑' },
    { value: 'light' as ActivityLevel, label: '轻度活动', desc: '每周轻度运动1-3次', emoji: '🚶' },
    { value: 'moderate' as ActivityLevel, label: '中度活动', desc: '每周中度运动3-5次', emoji: '🏃' },
    { value: 'active' as ActivityLevel, label: '高度活动', desc: '每周高强度运动6-7次', emoji: '🏋️' },
    { value: 'veryActive' as ActivityLevel, label: '极高活动', desc: '每天高强度运动或体力劳动', emoji: '⚡' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 via-white to-purple-50" ref={containerRef}>
      {/* 响应式布局：移动端全屏，桌面端充分利用宽度 */}
      <div className="px-4 py-6 lg:px-8 xl:px-12 2xl:px-16">
        
        {/* 头部区域 - 移动端优化 */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-3">
            {isEditMode ? '编辑个人档案' : '设置个人档案'}
          </h1>
          <p className="text-base text-slate-600">
            {isEditMode ? '更新您的个人信息和健康目标' : '为您制定个性化的健康计划'}
          </p>
        </div>

        {/* 步骤指示器 */}
        <div className="mb-8 sm:mb-12 step-indicator">
          <ul className="steps steps-horizontal w-full text-sm sm:text-base">
            <li className={`step ${step >= 1 ? 'step-primary' : ''}`} data-content={step > 1 ? '✓' : '1'}>
              <span className="hidden sm:inline">基本信息</span>
              <span className="sm:hidden">基本</span>
            </li>
            <li className={`step ${step >= 2 ? 'step-primary' : ''}`} data-content={step > 2 ? '✓' : '2'}>
              <span className="hidden sm:inline">目标设置</span>
              <span className="sm:hidden">目标</span>
            </li>
            <li className={`step ${step >= 3 ? 'step-primary' : ''}`} data-content={step > 3 ? '✓' : '3'}>
              <span className="hidden sm:inline">确认信息</span>
              <span className="sm:hidden">确认</span>
            </li>
          </ul>
        </div>

        {/* 主内容卡片 - 响应式优化 */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 max-w-none lg:max-w-4xl xl:max-w-5xl 2xl:max-w-6xl mx-auto">
          <div className="p-6 lg:p-8 xl:p-10">

            {/* 步骤1：基本信息 */}
            {step === 1 && (
              <div
                className="space-y-6 step-container"
                data-step="1"
                ref={(el) => { stepRefs.current[1] = el; }}
              >
                <div className="text-center mb-6">
                  <div className="text-5xl mb-4">👋</div>
                  <h2 className="text-xl font-bold mb-3 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    告诉我们关于你的信息
                  </h2>
                  <p className="text-sm text-slate-600">
                    我们需要一些基本信息来为你定制个性化的健康计划
                  </p>
                </div>

                <div className="space-y-6 lg:space-y-8">
                  {/* 姓名 - 与性别按钮组宽度对齐 */}
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text font-medium text-slate-700">姓名（可选）</span>
                    </label>
                    <input
                      type="text"
                      placeholder="请输入您的姓名"
                      className="input input-bordered h-12 text-base bg-white w-full"
                      value={data.name || ''}
                      onChange={(e) => updateField('name', e.target.value)}
                      onFocus={handleInputFocus}
                    />
                  </div>

                  {/* 身高体重年龄 - 响应式网格布局 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text text-base sm:text-lg font-medium">
                          身高 <span className="text-error">*</span>
                        </span>
                      </label>
                      <div className="join">
                        <input
                          type="number"
                          placeholder="170"
                          className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.height ? 'input-error' : ''}`}
                          value={data.height || ''}
                          onChange={(e) => updateField('height', Number(e.target.value))}
                          onFocus={handleInputFocus}
                        />
                        <div className="btn btn-lg join-item no-animation bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500">cm</div>
                      </div>
                      {errors.height && (
                        <label className="label">
                          <span className="label-text-alt text-error text-xs sm:text-sm">{errors.height}</span>
                        </label>
                      )}
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text text-base sm:text-lg font-medium">
                          体重 <span className="text-error">*</span>
                        </span>
                      </label>
                      <div className="join">
                        <input
                          type="number"
                          placeholder="65"
                          className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.weight ? 'input-error' : ''}`}
                          value={data.weight || ''}
                          onChange={(e) => updateField('weight', Number(e.target.value))}
                          onFocus={handleInputFocus}
                        />
                        <div className="btn btn-lg join-item no-animation bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500">kg</div>
                      </div>
                      {errors.weight && (
                        <label className="label">
                          <span className="label-text-alt text-error text-xs sm:text-sm">{errors.weight}</span>
                        </label>
                      )}
                    </div>

                    <div className="form-control">
                      <label className="label">
                        <span className="label-text text-base sm:text-lg font-medium">
                          年龄 <span className="text-error">*</span>
                        </span>
                      </label>
                      <div className="join">
                        <input
                          type="number"
                          placeholder="25"
                          className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.age ? 'input-error' : ''}`}
                          value={data.age || ''}
                          onChange={(e) => updateField('age', Number(e.target.value))}
                          onFocus={handleInputFocus}
                        />
                        <div className="btn btn-lg join-item no-animation bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500">岁</div>
                      </div>
                      {errors.age && (
                        <label className="label">
                          <span className="label-text-alt text-error text-xs sm:text-sm">{errors.age}</span>
                        </label>
                      )}
                    </div>
                  </div>

                  {/* 性别选择 */}
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text text-base sm:text-lg font-medium">
                        性别 <span className="text-error">*</span>
                      </span>
                    </label>
                    <div className="grid grid-cols-2 gap-3 sm:gap-4">
                      {(['male', 'female'] as Gender[]).map((gender) => (
                        <button
                          key={gender}
                          type="button"
                          onClick={(e) => { handleButtonClick(e); updateField('gender', gender); }}
                          className={`btn btn-lg h-20 sm:h-24 min-h-20 sm:min-h-24 ${
                            data.gender === gender ? 'btn-primary' : 'btn-outline'
                          }`}
                        >
                          <div className="flex flex-col items-center space-y-1 sm:space-y-2">
                            <div className="text-3xl sm:text-4xl">
                              {gender === 'male' ? '👨' : '👩'}
                            </div>
                            <span className="text-base sm:text-lg font-bold">
                              {gender === 'male' ? '男性' : '女性'}
                            </span>
                          </div>
                        </button>
                      ))}
                    </div>
                    {errors.gender && (
                      <label className="label">
                        <span className="label-text-alt text-error">{errors.gender}</span>
                      </label>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 步骤2：目标设置 */}
            {step === 2 && (
              <div className="space-y-8">
                <div className="text-center">
                  <div className="text-8xl mb-6">🎯</div>
                  <h2 className="text-3xl font-bold mb-2">设定你的目标</h2>
                  <p className="text-base-content/60">告诉我们你想要达到什么目标</p>
                </div>

                <div className="max-w-none lg:max-w-4xl mx-auto space-y-6 lg:space-y-8">
                  {/* 目标天数 - 全宽度自适应 */}
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-lg font-medium">
                        目标天数 <span className="text-error">*</span>
                      </span>
                    </label>
                    <div className="join w-full">
                      <input
                        type="number"
                        placeholder="90"
                        className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.targetDays ? 'input-error' : ''}`}
                        value={data.targetDays || ''}
                        onChange={(e) => updateField('targetDays', Number(e.target.value))}
                      />
                      <div className="btn btn-lg join-item no-animation bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500">天</div>
                    </div>
                    {errors.targetDays && (
                      <label className="label">
                        <span className="label-text-alt text-error">{errors.targetDays}</span>
                      </label>
                    )}
                  </div>

                  {/* 活动水平 - 全宽度自适应 */}
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-lg font-medium">
                        活动水平 <span className="text-error">*</span>
                      </span>
                    </label>
                    <div className="space-y-3 w-full">
                      {activityOptions.map((option) => (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => updateField('activityLevel', option.value)}
                          className={`btn btn-lg w-full justify-start h-auto p-6 ${
                            data.activityLevel === option.value ? 'btn-primary' : 'btn-outline'
                          }`}
                        >
                          <div className="flex items-center space-x-4 w-full">
                            <span className="text-3xl">{option.emoji}</span>
                            <div className="text-left">
                              <div className="text-lg font-bold">{option.label}</div>
                              <div className="text-sm opacity-70">{option.desc}</div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                    {errors.activityLevel && (
                      <label className="label">
                        <span className="label-text-alt text-error">{errors.activityLevel}</span>
                      </label>
                    )}
                  </div>

                  {/* 目标体重 - 全宽度自适应 */}
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-lg font-medium">
                        目标体重 <span className="text-error">*</span>
                      </span>
                    </label>
                    <div className="join w-full">
                      <input
                        type="number"
                        placeholder="60"
                        className={`input input-bordered input-lg join-item flex-1 text-lg ${errors.targetWeight ? 'input-error' : ''}`}
                        value={data.targetWeight || ''}
                        onChange={(e) => updateField('targetWeight', Number(e.target.value))}
                      />
                      <button
                        type="button"
                        onClick={calculateRecommendedWeight}
                        disabled={!data.targetDays || !data.height || !data.weight || !data.activityLevel}
                        className="btn btn-lg join-item bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 hover:from-blue-600 hover:to-indigo-700 disabled:opacity-50 whitespace-nowrap"
                      >
                        <span className="flex items-center gap-2">
                          <span>kg</span>
                          <span className="text-xs opacity-75">|</span>
                          <span>推荐</span>
                        </span>
                      </button>
                    </div>
                    {errors.targetWeight && (
                      <label className="label">
                        <span className="label-text-alt text-error">{errors.targetWeight}</span>
                      </label>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 步骤3：确认信息 - 移动端优化 */}
            {step === 3 && (
              <div
                className="space-y-6 step-container"
                data-step="3"
                ref={(el) => { stepRefs.current[3] = el; }}
              >
                <div className="text-center mb-6">
                  <div className="text-5xl mb-4">✨</div>
                  <h2 className="text-xl font-bold mb-3 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    确认你的信息
                  </h2>
                  <p className="text-sm text-slate-600">请确认以下信息无误</p>
                </div>

                <div className="space-y-4 lg:space-y-6">
                  {/* 基本信息总结 - 响应式优化 */}
                  <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-5 lg:p-6">
                    <h3 className="text-lg font-bold mb-4 text-indigo-700 flex items-center">
                      <span className="text-xl mr-2">👤</span>
                      基本信息
                    </h3>
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
                      <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4">
                        <div className="text-xs text-slate-600 mb-1">姓名</div>
                        <div className="font-semibold text-slate-800">{data.name || '未设置'}</div>
                      </div>
                      <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4">
                        <div className="text-xs text-slate-600 mb-1">性别</div>
                        <div className="font-semibold text-slate-800">
                          {data.gender === 'male' ? '👨 男性' : '👩 女性'}
                        </div>
                      </div>
                      <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4">
                        <div className="text-xs text-slate-600 mb-1">年龄</div>
                        <div className="font-semibold text-slate-800">{data.age} 岁</div>
                      </div>
                      <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4">
                        <div className="text-xs text-slate-600 mb-1">身高</div>
                        <div className="font-semibold text-slate-800">{data.height} cm</div>
                      </div>
                    </div>
                    <div className="mt-4 lg:mt-6">
                      <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4 max-w-xs">
                        <div className="text-xs text-slate-600 mb-1">当前体重</div>
                        <div className="font-semibold text-slate-800 text-lg">
                          {data.weight ? `${data.weight % 1 === 0 ? data.weight : parseFloat(data.weight.toFixed(1))} kg` : '0 kg'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 目标信息总结 - 移动端优化 */}
                  <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-5">
                    <h3 className="text-lg font-bold mb-4 text-purple-700 flex items-center">
                      <span className="text-xl mr-2">🎯</span>
                      目标设置
                    </h3>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-3">
                        <div className="text-xs text-slate-600 mb-1">目标体重</div>
                        <div className="font-semibold text-slate-800 text-lg">
                          {data.targetWeight ? `${data.targetWeight % 1 === 0 ? data.targetWeight : parseFloat(data.targetWeight.toFixed(1))} kg` : '0 kg'}
                        </div>
                      </div>
                      <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-3">
                        <div className="text-xs text-slate-600 mb-1">目标天数</div>
                        <div className="font-semibold text-slate-800 text-lg">{data.targetDays} 天</div>
                      </div>
                    </div>
                    <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-4">
                      <div className="text-xs text-slate-600 mb-2">活动水平</div>
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl">
                          {activityOptions.find(opt => opt.value === data.activityLevel)?.emoji}
                        </span>
                        <div>
                          <div className="font-semibold text-slate-800">
                            {activityOptions.find(opt => opt.value === data.activityLevel)?.label}
                          </div>
                          <div className="text-xs text-slate-600">
                            {activityOptions.find(opt => opt.value === data.activityLevel)?.desc}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 准备完成提示 - 移动端优化 */}
                  <div className="bg-gradient-to-r from-emerald-50 to-cyan-50 rounded-2xl border border-emerald-200 p-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-emerald-800 mb-1">准备就绪！</h3>
                        <p className="text-sm text-emerald-700">
                          点击完成按钮，系统将为您计算个性化的营养计划。
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

          </div>
        </div>

        {/* 底部按钮 - 专业视觉优化 */}
        <div className="flex justify-between items-center mt-8 navigation-buttons gap-4">
          <div className="flex-1">
            {step > 1 && (
              <button
                onClick={(e) => { handleButtonClick(e); prevStep(); }}
                className="btn btn-outline btn-lg w-full sm:w-auto h-14 min-h-14 px-6 sm:px-8 group whitespace-nowrap"
                disabled={loading}
              >
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-5 h-5 transition-transform group-hover:-translate-x-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span className="font-medium">上一步</span>
                </div>
              </button>
            )}
          </div>

          <div className="flex-1 flex justify-end">
            {step < 3 ? (
              <button
                onClick={(e) => { handleButtonClick(e); nextStep(); }}
                className="btn btn-primary btn-lg w-full sm:w-auto h-14 min-h-14 px-6 sm:px-8 group whitespace-nowrap"
                disabled={loading}
              >
                <div className="flex items-center justify-center space-x-2">
                  <span className="font-medium">下一步</span>
                  <svg className="w-5 h-5 transition-transform group-hover:translate-x-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </button>
            ) : (
              <button
                onClick={(e) => { handleButtonClick(e); handleSubmit(); }}
                className="btn btn-primary btn-lg submit-button w-full sm:w-auto h-14 min-h-14 px-6 sm:px-8 group whitespace-nowrap"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <span className="loading loading-spinner loading-sm flex-shrink-0"></span>
                    <span className="font-medium">{isEditMode ? '更新中...' : '设置中...'}</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <span className="text-xl flex-shrink-0">{isEditMode ? '💾' : '🎉'}</span>
                    <span className="font-medium">{isEditMode ? '保存更新' : '完成设置'}</span>
                  </div>
                )}
              </button>
            )}
          </div>
        </div>

      </div>
    </div>
  );
};

export default ProfileSetupForm;