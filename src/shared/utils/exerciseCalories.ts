/**
 * 运动消耗卡路里计算工具
 * 基于标准体重70kg成年人的运动消耗数据
 */

// 运动类型及其每分钟消耗的卡路里（基于70kg体重）
export const EXERCISE_CALORIES_PER_MINUTE = {
  running: 12,        // 跑步（8km/h）
  walking: 4,         // 快走（6km/h）
  swimming: 10,       // 游泳
  cycling: 8,         // 骑车（16km/h）
  jumping: 15,        // 跳绳
  yoga: 3,           // 瑜伽
  basketball: 9,      // 篮球
  tennis: 8,         // 网球
  dancing: 6,        // 跳舞
  climbing: 11       // 爬楼梯
} as const;

// 运动类型的中文名称
export const EXERCISE_NAMES = {
  running: '跑步',
  walking: '快走',
  swimming: '游泳',
  cycling: '骑车',
  jumping: '跳绳',
  yoga: '瑜伽',
  basketball: '篮球',
  tennis: '网球',
  dancing: '跳舞',
  climbing: '爬楼梯'
} as const;

export type ExerciseType = keyof typeof EXERCISE_CALORIES_PER_MINUTE;

/**
 * 计算消耗指定卡路里需要的运动时间
 */
export function calculateExerciseTime(calories: number, exerciseType: ExerciseType): number {
  const caloriesPerMinute = EXERCISE_CALORIES_PER_MINUTE[exerciseType];
  return Math.ceil(calories / caloriesPerMinute);
}

/**
 * 获取消耗指定卡路里的所有运动建议
 */
export function getExerciseSuggestions(calories: number): Array<{
  type: ExerciseType;
  name: string;
  minutes: number;
  icon: string;
}> {
  const suggestions = [
    { type: 'running' as ExerciseType, icon: '🏃‍♂️' },
    { type: 'walking' as ExerciseType, icon: '🚶‍♂️' },
    { type: 'swimming' as ExerciseType, icon: '🏊‍♂️' },
    { type: 'cycling' as ExerciseType, icon: '🚴‍♂️' },
    { type: 'jumping' as ExerciseType, icon: '🪢' },
    { type: 'dancing' as ExerciseType, icon: '💃' }
  ];

  return suggestions.map(({ type, icon }) => ({
    type,
    name: EXERCISE_NAMES[type],
    minutes: calculateExerciseTime(calories, type),
    icon
  }));
}

/**
 * 格式化运动时间显示
 */
export function formatExerciseTime(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}分钟`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}小时`;
  }
  
  return `${hours}小时${remainingMinutes}分钟`;
}

/**
 * 获取运动建议的友好提示文案
 */
export function getExerciseAdviceText(calories: number): string {
  if (calories < 100) {
    return '这些卡路里很容易消耗，简单运动就能搞定！';
  } else if (calories < 300) {
    return '适量运动就能消耗这些卡路里，保持活力！';
  } else if (calories < 500) {
    return '需要一些运动来消耗，但完全可以做到！';
  } else {
    return '卡路里较高，建议适量食用并增加运动量。';
  }
}
