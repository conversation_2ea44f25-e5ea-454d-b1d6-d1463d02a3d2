import React, { useRef, useState, useCallback } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from '@/shared/components';
import { 
  CameraIcon, 
  PhotoIcon, 
  ArrowPathIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { smartCompressImage } from '@/shared/utils/image';
import { cn } from '@/shared/utils/format';
import { useToast } from '@/shared/hooks/useToast';

interface CameraProps {
  isOpen: boolean;
  onClose: () => void;
  onCapture: (file: File) => void;
  maxSizeMB?: number;
}

const Camera: React.FC<CameraProps> = ({
  isOpen,
  onClose,
  onCapture,
  maxSizeMB = 30
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showError, showWarning, showInfo } = useToast();

  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');

  // 处理相机错误的详细函数
  const handleCameraError = (err: any) => {
    console.error('相机错误详情:', err);

    if (err.name === 'NotAllowedError') {
      setError('相机权限被拒绝');
      showError('相机权限被拒绝，请在浏览器设置中允许相机访问', 5000);
    } else if (err.name === 'NotFoundError') {
      setError('未找到相机设备');
      showError('未检测到相机设备，请检查设备连接', 5000);
    } else if (err.name === 'NotReadableError') {
      setError('相机被占用');
      showError('相机正被其他应用使用，请关闭其他应用后重试', 5000);
    } else if (err.name === 'OverconstrainedError') {
      setError('相机配置不支持');
      showWarning('当前相机不支持请求的配置，正在尝试降级设置', 4000);
    } else if (err.name === 'SecurityError') {
      setError('安全限制');
      showError('由于安全限制无法访问相机，请确保使用HTTPS连接', 5000);
    } else if (err.name === 'AbortError') {
      setError('操作被中断');
      showWarning('相机启动被中断，请重试', 3000);
    } else {
      setError('相机启动失败');
      showError('相机启动失败，请检查设备和权限设置', 4000);
    }
  };

  // 检查浏览器支持
  const checkBrowserSupport = () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setError('浏览器不支持相机功能');
      showError('当前浏览器不支持相机功能，请使用现代浏览器', 5000);
      return false;
    }
    return true;
  };

  // 启动相机
  const startCamera = useCallback(async () => {
    try {
      setError(null);
      setIsLoading(true);

      // 检查浏览器支持
      if (!checkBrowserSupport()) {
        return;
      }

      // 首先尝试高质量配置
      let constraints = {
        video: {
          facingMode,
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      };

      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;
          setStream(mediaStream);
          showInfo('相机启动成功', 2000);
        }
      } catch (highQualityError) {
        // 如果高质量配置失败，尝试基础配置
        console.warn('高质量配置失败，尝试基础配置:', highQualityError);

        constraints = {
          video: {
            facingMode,
            width: { ideal: 1920 },
            height: { ideal: 1080 }
          }
        };

        try {
          const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

          if (videoRef.current) {
            videoRef.current.srcObject = mediaStream;
            setStream(mediaStream);
            showWarning('相机以基础质量启动', 3000);
          }
        } catch (basicError) {
          // 如果基础配置也失败，尝试最简配置
          console.warn('基础配置失败，尝试最简配置:', basicError);

          try {
            const mediaStream = await navigator.mediaDevices.getUserMedia({ video: true });

            if (videoRef.current) {
              videoRef.current.srcObject = mediaStream;
              setStream(mediaStream);
              showWarning('相机以最简配置启动', 3000);
            }
          } catch (finalError) {
            handleCameraError(finalError);
          }
        }
      }
    } catch (err) {
      handleCameraError(err);
    } finally {
      setIsLoading(false);
    }
  }, [facingMode, showError, showWarning, showInfo]);

  // 重试启动相机
  const retryCamera = useCallback(() => {
    setError(null);
    startCamera();
  }, [startCamera]);

  // 检查权限状态
  const checkPermissionStatus = useCallback(async () => {
    try {
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });

        if (permission.state === 'denied') {
          setError('相机权限被拒绝');
          showError('相机权限被拒绝，请在浏览器设置中允许相机访问', 5000);
          return false;
        } else if (permission.state === 'prompt') {
          showInfo('即将请求相机权限，请允许访问', 3000);
        }
      }
      return true;
    } catch (err) {
      console.warn('无法检查权限状态:', err);
      return true; // 如果无法检查权限，继续尝试
    }
  }, [showError, showInfo]);

  // 停止相机
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  }, [stream]);

  // 拍照
  const takePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // 设置canvas尺寸
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // 绘制视频帧到canvas
    context.drawImage(video, 0, 0);

    // 转换为base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    setCapturedImage(imageData);
    stopCamera();
  }, [stopCamera]);

  // 重新拍照
  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    startCamera();
  }, [startCamera]);

  // 确认使用照片
  const confirmPhoto = useCallback(async () => {
    if (!capturedImage) return;

    try {
      setIsLoading(true);

      // 将base64转换为File对象
      const response = await fetch(capturedImage);
      const blob = await response.blob();
      const file = new File([blob], `photo_${Date.now()}.jpg`, {
        type: 'image/jpeg',
        lastModified: Date.now()
      });

      // 压缩图片
      const compressedFile = await smartCompressImage(file, maxSizeMB);
      
      onCapture(compressedFile);
      handleClose();
    } catch (err) {
      console.error('处理照片失败:', err);
      setError('照片处理失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [capturedImage, maxSizeMB, onCapture]);

  // 从相册选择
  const selectFromGallery = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // 处理文件选择
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsLoading(true);

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        setError('请选择图片文件');
        return;
      }

      // 压缩图片
      const compressedFile = await smartCompressImage(file, maxSizeMB);
      
      onCapture(compressedFile);
      handleClose();
    } catch (err) {
      console.error('处理图片失败:', err);
      setError('图片处理失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [maxSizeMB, onCapture]);

  // 切换摄像头
  const switchCamera = useCallback(() => {
    setFacingMode(prev => prev === 'user' ? 'environment' : 'user');
    if (stream) {
      stopCamera();
      // 延迟启动新摄像头
      setTimeout(startCamera, 100);
    }
  }, [stream, stopCamera, startCamera]);

  // 关闭相机
  const handleClose = useCallback(() => {
    stopCamera();
    setCapturedImage(null);
    setError(null);
    onClose();
  }, [stopCamera, onClose]);

  // 当模态框打开时启动相机
  React.useEffect(() => {
    if (isOpen && !capturedImage) {
      // 先检查权限状态，再启动相机
      checkPermissionStatus().then(canProceed => {
        if (canProceed) {
          startCamera();
        }
      });
    }

    return () => {
      if (!isOpen) {
        stopCamera();
      }
    };
  }, [isOpen, capturedImage, startCamera, stopCamera, checkPermissionStatus]);

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        size="full"
        showCloseButton={false}
      >
        <div className="relative h-full bg-black">
          {/* 错误提示 */}
          {error && (
            <div className="absolute top-4 left-4 right-4 z-10 bg-red-500 text-white p-4 rounded-lg shadow-lg">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="font-medium mb-1">相机启动失败</div>
                  <div className="text-sm opacity-90">{error}</div>
                </div>
                <button
                  onClick={retryCamera}
                  className="ml-3 bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded text-sm font-medium transition-colors"
                >
                  重试
                </button>
              </div>
              <div className="mt-3 text-xs opacity-75">
                💡 提示：如果问题持续，请尝试刷新页面或使用相册选择功能
              </div>
            </div>
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="absolute inset-0 z-20 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="bg-white rounded-lg p-4 flex items-center gap-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                <span>处理中...</span>
              </div>
            </div>
          )}

          {capturedImage ? (
            // 照片预览
            <div className="relative h-full flex flex-col">
              <img
                src={capturedImage}
                alt="拍摄的照片"
                className="flex-1 object-contain"
              />
              
              {/* 预览操作按钮 */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
                <div className="flex justify-center gap-4">
                  <Button
                    variant="secondary"
                    onClick={retakePhoto}
                    className="flex items-center gap-2"
                  >
                    <ArrowPathIcon className="h-5 w-5" />
                    重拍
                  </Button>
                  <Button
                    variant="primary"
                    onClick={confirmPhoto}
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    <CheckIcon className="h-5 w-5" />
                    使用照片
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // 相机视图
            <div className="relative h-full flex flex-col">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="flex-1 object-cover w-full"
              />
              
              {/* 相机控制按钮 */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
                <div className="flex items-center justify-between">
                  {/* 关闭按钮 */}
                  <button
                    onClick={handleClose}
                    className="p-3 bg-black bg-opacity-50 rounded-full text-white"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>

                  {/* 拍照按钮 */}
                  <button
                    onClick={takePhoto}
                    disabled={!stream || isLoading}
                    className={cn(
                      "w-16 h-16 rounded-full border-4 border-white bg-white",
                      "disabled:opacity-50 disabled:cursor-not-allowed",
                      "active:scale-95 transition-transform"
                    )}
                  >
                    <CameraIcon className="h-8 w-8 text-gray-800 mx-auto" />
                  </button>

                  {/* 切换摄像头按钮 */}
                  <button
                    onClick={switchCamera}
                    disabled={!stream || isLoading}
                    className="p-3 bg-black bg-opacity-50 rounded-full text-white disabled:opacity-50"
                  >
                    <ArrowPathIcon className="h-6 w-6" />
                  </button>
                </div>

                {/* 相册选择按钮 */}
                <div className="flex justify-center mt-4">
                  <button
                    onClick={selectFromGallery}
                    className="flex items-center gap-2 px-4 py-2 bg-black bg-opacity-50 rounded-full text-white"
                  >
                    <PhotoIcon className="h-5 w-5" />
                    从相册选择
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 隐藏的canvas用于拍照 */}
          <canvas ref={canvasRef} className="hidden" />
        </div>
      </Modal>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />
    </>
  );
};

export default Camera;