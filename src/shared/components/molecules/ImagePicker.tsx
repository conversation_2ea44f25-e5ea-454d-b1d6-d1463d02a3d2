import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/shared/components';
import { 
  CameraIcon, 
  PhotoIcon, 
  XMarkIcon,
  ArrowUpTrayIcon
} from '@heroicons/react/24/outline';
import { smartCompressImage, createThumbnail } from '@/shared/utils/image';
import { cn } from '@/shared/utils/format';

interface ImagePickerProps {
  onImageSelect: (file: File, thumbnail?: string) => void;
  maxSizeMB?: number;
  className?: string;
  disabled?: boolean;
  currentImage?: string;
  placeholder?: string;
}

const ImagePicker: React.FC<ImagePickerProps> = ({
  onImageSelect,
  maxSizeMB = 30,
  className,
  disabled = false,
  currentImage,
  placeholder = '点击添加图片'
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(currentImage || null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);



  // 处理相机拍照 - 使用HTML5 input
  const handleCameraCapture = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsProcessing(true);

      // 创建缩略图
      const thumbnail = await createThumbnail(file, 200);
      setPreviewImage(thumbnail);

      onImageSelect(file, thumbnail);
    } catch (error) {
      console.error('处理相机照片失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // 处理文件选择
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsProcessing(true);

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        alert('请选择图片文件');
        return;
      }

      // 压缩图片
      const compressedFile = await smartCompressImage(file, maxSizeMB);
      
      // 创建缩略图
      const thumbnail = await createThumbnail(compressedFile, 200);
      setPreviewImage(thumbnail);
      
      onImageSelect(compressedFile, thumbnail);
    } catch (error) {
      console.error('处理图片失败:', error);
      alert('图片处理失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  // 打开文件选择器
  const openFileSelector = () => {
    fileInputRef.current?.click();
  };

  // 清除图片
  const clearImage = () => {
    setPreviewImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={cn('space-y-3', className)}>
      {/* 图片预览区域 */}
      <div className="relative">
        {previewImage ? (
          <div className="relative group">
            <img
              src={previewImage}
              alt="预览图片"
              className="w-full h-48 object-cover rounded-lg border border-gray-200"
            />
            
            {/* 删除按钮 */}
            <button
              onClick={clearImage}
              disabled={disabled || isProcessing}
              className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity disabled:opacity-50"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>

            {/* 处理中遮罩 */}
            {isProcessing && (
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                <div className="bg-white rounded-lg p-3 flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                  <span className="text-sm">处理中...</span>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center text-gray-500 bg-gray-50">
            <ArrowUpTrayIcon className="h-12 w-12 mb-2" />
            <p className="text-sm font-medium">{placeholder}</p>
            <p className="text-xs mt-1">支持 JPG、PNG、WebP 格式</p>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="grid grid-cols-2 gap-3">
        <Button
          variant="secondary"
          onClick={() => cameraInputRef.current?.click()}
          disabled={disabled || isProcessing}
          className="flex items-center justify-center gap-2"
        >
          <CameraIcon className="h-5 w-5" />
          拍照
        </Button>
        
        <Button
          variant="secondary"
          onClick={openFileSelector}
          disabled={disabled || isProcessing}
          className="flex items-center justify-center gap-2"
        >
          <PhotoIcon className="h-5 w-5" />
          相册
        </Button>
      </div>

      {/* 文件大小提示 */}
      <p className="text-xs text-gray-500 text-center">
        支持最大 {maxSizeMB}MB 图片，自动优化以保证AI识别清晰度
      </p>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* 隐藏的相机输入 - 使用HTML5 capture */}
      <input
        ref={cameraInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={handleCameraCapture}
        className="hidden"
      />
    </div>
  );
};

export default ImagePicker;