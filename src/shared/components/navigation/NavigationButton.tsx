import React from 'react';

interface NavigationButtonProps {
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  onClick: () => void;
  ariaLabel: string;
}

const NavigationButton: React.FC<NavigationButtonProps> = ({
  icon,
  label,
  isActive,
  onClick,
  ariaLabel
}) => {
  return (
    <button
      className={`${isActive ? 'dock-active' : ''}`}
      onClick={onClick}
      aria-label={ariaLabel}
    >
      {icon}
      <span className="dock-label">{label}</span>
    </button>
  );
};

export default NavigationButton;
