import React from 'react';
import { cn } from '@/shared/utils/format';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className,
    label,
    error,
    helperText,
    leftIcon,
    rightIcon,
    fullWidth = false,
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    
    const inputClasses = cn(
      'block w-full rounded-lg border px-4 py-3 text-base',
      'placeholder:text-gray-400',
      'focus:outline-none focus:ring-2 focus:ring-offset-1',
      'disabled:cursor-not-allowed disabled:opacity-50',
      'transition-colors duration-200',
      // 移动端优化
      'touch-manipulation',
      'min-h-[44px]', // 确保足够的触控区域
      'text-[16px]',  // 防止iOS缩放
      // 错误状态
      error
        ? 'border-red-300 text-red-900'
        : 'border-gray-300 text-gray-900',
      // 图标间距
      leftIcon && 'pl-12',
      rightIcon && 'pr-12',
      className
    );

    // 获取内联样式
    const getInputStyles = (): React.CSSProperties => {
      if (error) {
        return {
          borderColor: '#fca5a5', // red-300
          color: '#7f1d1d' // red-900
        };
      }
      return {
        borderColor: '#d1d5db', // gray-300
        color: '#111827' // gray-900
      };
    };

    const getFocusStyles = () => {
      return {
        onFocus: (e: React.FocusEvent<HTMLInputElement>) => {
          if (error) {
            e.target.style.borderColor = '#ef4444'; // red-500
            e.target.style.boxShadow = '0 0 0 2px rgba(239, 68, 68, 0.2)';
          } else {
            e.target.style.borderColor = '#16a34a'; // green-600
            e.target.style.boxShadow = '0 0 0 2px rgba(22, 163, 74, 0.2)';
          }
        },
        onBlur: (e: React.FocusEvent<HTMLInputElement>) => {
          e.target.style.borderColor = error ? '#fca5a5' : '#d1d5db';
          e.target.style.boxShadow = 'none';
        }
      };
    };

    const containerClasses = cn(
      'relative',
      fullWidth ? 'w-full' : 'w-auto'
    );

    return (
      <div className={containerClasses}>
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className="h-5 w-5 text-gray-400">
                {leftIcon}
              </div>
            </div>
          )}
          
          <input
            ref={ref}
            id={inputId}
            className={inputClasses}
            style={getInputStyles()}
            {...getFocusStyles()}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <div className="h-5 w-5 text-gray-400">
                {rightIcon}
              </div>
            </div>
          )}
        </div>
        
        {(error || helperText) && (
          <p
            className={cn(
              'mt-1 text-sm',
              error ? '' : 'text-gray-500'
            )}
            style={error ? { color: '#dc2626' } : {}}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input };
export default Input;