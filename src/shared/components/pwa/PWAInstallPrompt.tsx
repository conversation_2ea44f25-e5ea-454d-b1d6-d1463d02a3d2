import React, { useState, useEffect } from 'react';
import { LocalStorage } from '@/shared/utils/storage';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAInstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [canInstall, setCanInstall] = useState(false);
  const [userDismissedPermanently, setUserDismissedPermanently] = useState(false);

  // 存储键名
  const PWA_DISMISSED_KEY = 'pwa-install-dismissed';

  useEffect(() => {
    // 检查用户是否已永久关闭提示
    const isDismissed = LocalStorage.get<boolean>(PWA_DISMISSED_KEY, false);
    if (isDismissed) {
      setUserDismissedPermanently(true);
      return;
    }

    // 检查是否已经安装
    const checkIfInstalled = () => {
      // 检查是否在PWA模式下运行
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
        return true;
      }

      // 检查是否在iOS Safari中添加到主屏幕
      if ((window.navigator as any).standalone === true) {
        setIsInstalled(true);
        return true;
      }

      return false;
    };

    const isAlreadyInstalled = checkIfInstalled();

    // 监听beforeinstallprompt事件
    const handleBeforeInstallPrompt = (e: Event) => {
      console.log('beforeinstallprompt事件触发');
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setCanInstall(true);

      // 如果没有安装且用户没有永久关闭，延迟显示安装提示
      if (!isAlreadyInstalled && !isDismissed) {
        setTimeout(() => {
          setShowInstallPrompt(true);
        }, 5000); // 5秒后显示，减少等待时间
      }
    };

    // 监听应用安装事件
    const handleAppInstalled = () => {
      console.log('PWA已安装');
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setCanInstall(false);
      setDeferredPrompt(null);
    };

    // 监听显示模式变化
    const handleDisplayModeChange = () => {
      checkIfInstalled();
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // 监听显示模式变化
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    mediaQuery.addEventListener('change', handleDisplayModeChange);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      mediaQuery.removeEventListener('change', handleDisplayModeChange);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      // 如果没有deferredPrompt，提供手动安装指导
      showManualInstallGuide();
      return;
    }

    try {
      console.log('触发PWA安装提示');
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;

      console.log('用户选择结果:', outcome);

      if (outcome === 'accepted') {
        console.log('用户接受了安装提示');
        setIsInstalled(true);
      } else {
        console.log('用户拒绝了安装提示');
      }

      setDeferredPrompt(null);
      setCanInstall(false);
      setShowInstallPrompt(false);
    } catch (error) {
      console.error('安装提示失败:', error);
      showManualInstallGuide();
    }
  };

  const showManualInstallGuide = () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);

    let message = '请手动安装应用：\n\n';

    if (isIOS) {
      message += '1. 点击浏览器底部的分享按钮\n2. 选择"添加到主屏幕"\n3. 点击"添加"';
    } else if (isAndroid) {
      message += '1. 点击浏览器菜单（三个点）\n2. 选择"添加到主屏幕"或"安装应用"\n3. 点击"安装"';
    } else {
      message += '1. 点击地址栏右侧的安装图标\n2. 或在浏览器菜单中选择"安装应用"';
    }

    alert(message);
  };

  const handleDismiss = () => {
    setShowInstallPrompt(false);
    // 1小时后再次显示，减少干扰
    setTimeout(() => {
      if (!isInstalled && canInstall && !userDismissedPermanently) {
        setShowInstallPrompt(true);
      }
    }, 60 * 60 * 1000); // 1小时
  };

  const handlePermanentDismiss = () => {
    setShowInstallPrompt(false);
    setUserDismissedPermanently(true);
    LocalStorage.set(PWA_DISMISSED_KEY, true);
  };

  // 如果已安装或用户永久关闭，不显示组件
  if (isInstalled || userDismissedPermanently) {
    return null;
  }

  // 如果没有显示提示或者既没有deferredPrompt也不能安装，不显示组件
  if (!showInstallPrompt || (!deferredPrompt && !canInstall)) {
    return null;
  }

  return (
    <div className="fixed bottom-20 left-4 right-4 z-50 md:left-auto md:right-4 md:w-80">
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200/50 p-4 animate-slide-up">
        <div className="flex items-start gap-3">
          <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>

          <div className="flex-1 min-w-0">
            <h3 className="font-bold text-gray-900 text-sm mb-1">
              安装KCal Tracker
            </h3>
            <p className="text-xs text-gray-600 mb-3">
              添加到主屏幕，享受原生应用体验
              {deferredPrompt ? '' : '（需要手动操作）'}
            </p>

            <div className="flex gap-2">
              <button
                onClick={handleInstallClick}
                className="flex-1 bg-emerald-600 text-white text-xs py-3 px-2 rounded-xl hover:bg-emerald-700 transition-colors font-medium shadow-lg min-h-[44px] flex items-center justify-center whitespace-nowrap"
              >
                {deferredPrompt ? '立即安装' : '安装指导'}
              </button>
              <button
                onClick={handleDismiss}
                className="flex-1 bg-gray-100 text-gray-700 text-xs py-3 px-2 rounded-xl hover:bg-gray-200 transition-colors min-h-[44px] flex items-center justify-center whitespace-nowrap"
              >
                稍后提醒
              </button>
            </div>
          </div>

          <button
            onClick={handlePermanentDismiss}
            className="text-gray-400 hover:text-gray-600 transition-colors p-2 min-h-[44px] min-w-[44px] flex items-center justify-center"
            title="不再提示"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PWAInstallPrompt;
