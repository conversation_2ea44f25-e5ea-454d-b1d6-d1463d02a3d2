import React, { useEffect, useRef } from 'react';
import { animate } from 'animejs';

interface DockAnimationTestProps {
  children: React.ReactNode;
}

/**
 * Anime.js v4兼容性测试组件
 * 验证动画不会影响底部导航栏的fixed定位
 */
export const DockAnimationTest: React.FC<DockAnimationTestProps> = ({ children }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 测试页面内容动画 - 确保不影响dock
    const animation = animate(container, {
      opacity: [0, 1],
      duration: 800,
      easing: 'easeOutQuart',
      complete: () => {
        // 验证dock位置未受影响
        const dock = document.querySelector('.dock') as HTMLElement;
        if (dock) {
          const computedStyle = window.getComputedStyle(dock);
          console.log('🔍 Dock position after animation:', {
            position: computedStyle.position,
            bottom: computedStyle.bottom,
            zIndex: computedStyle.zIndex,
            transform: computedStyle.transform
          });
        }
      }
    });

    return () => {
      if (animation && typeof animation.pause === 'function') {
        animation.pause();
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="page-content"
      style={{
        opacity: 0,
        // 确保不创建层叠上下文
        position: 'relative',
        zIndex: 1,
        // 避免transform影响
        transform: 'none'
      }}
    >
      {children}
    </div>
  );
};

export default DockAnimationTest;
