import React from 'react';

interface AnimationContainerProps {
  children: React.ReactNode;
  className?: string;
  enableAnimations?: boolean;
}

/**
 * 动画容器 - 层叠上下文隔离解决方案
 * 
 * 2025年最佳实践：
 * 1. 将动画容器与固定导航栏完全隔离
 * 2. 使用CSS containment避免影响外部元素
 * 3. 提供动画开关机制
 */
const AnimationContainer: React.FC<AnimationContainerProps> = ({
  children,
  className = '',
  enableAnimations = true
}) => {
  return (
    <div
      className={`animation-container ${className}`}
      style={{
        // CSS containment - 隔离动画影响
        contain: 'layout style paint',
        // 确保不影响fixed定位的子元素
        position: 'relative',
        zIndex: 'auto',
        // 动画开关
        ...(enableAnimations ? {} : { 
          transform: 'none !important',
          animation: 'none !important' 
        })
      }}
    >
      {children}
    </div>
  );
};

export default AnimationContainer;
