import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useAccessibleAnimation } from '@/shared/hooks/useAnimation';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
}

export const PageTransition: React.FC<PageTransitionProps> = ({ 
  children, 
  className = '' 
}) => {
  const location = useLocation();
  const containerRef = useRef<HTMLDivElement>(null);
  const { prefersReducedMotion, createAccessibleAnimation } = useAccessibleAnimation();
  const previousLocationRef = useRef(location.pathname);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Check if this is a route change
    const isRouteChange = previousLocationRef.current !== location.pathname;
    
    if (isRouteChange) {
      // Page transition animation - 避免transform影响fixed定位
      createAccessibleAnimation(container, {
        opacity: [0, 1],
        duration: 600,
        easing: 'easeOutQuart',
        begin: () => {
          container.style.opacity = '0';
        },
        complete: () => {
          // 确保没有transform属性
          container.style.transform = 'none';
        }
      });

      // Update previous location
      previousLocationRef.current = location.pathname;
    } else {
      // Initial load - just ensure visibility without transform
      container.style.opacity = '1';
      container.style.transform = 'none';
    }
  }, [location.pathname, createAccessibleAnimation]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        opacity: prefersReducedMotion ? 1 : 0,
        // 完全避免transform属性，防止创建层叠上下文
        transform: 'none',
        // 确保不影响fixed定位的子元素
        position: 'relative',
        zIndex: 'auto'
      }}
    >
      {children}
    </div>
  );
};

export default PageTransition;