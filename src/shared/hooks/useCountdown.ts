import { useState, useEffect, useCallback } from 'react';
import { getCurrentSnackTimeInfo, SnackTimeInfo } from '@/shared/utils/snackTimeUtils';

export interface CountdownInfo {
  snackInfo: SnackTimeInfo;
  timeUntilNext: string;
  isSnackTime: boolean;
  currentPeriodName: string;
  nextPeriodName: string;
}

export const useCountdown = (updateInterval: number = 60000): CountdownInfo => {
  const [countdownInfo, setCountdownInfo] = useState<CountdownInfo>(() => {
    const snackInfo = getCurrentSnackTimeInfo();
    return {
      snackInfo,
      timeUntilNext: snackInfo.timeUntilNext,
      isSnackTime: snackInfo.isSnackTime,
      currentPeriodName: snackInfo.currentPeriod?.name || '',
      nextPeriodName: snackInfo.nextPeriod?.name || ''
    };
  });

  const updateCountdown = useCallback(() => {
    const snackInfo = getCurrentSnackTimeInfo();
    setCountdownInfo({
      snackInfo,
      timeUntilNext: snackInfo.timeUntilNext,
      isSnackTime: snackInfo.isSnackTime,
      currentPeriodName: snackInfo.currentPeriod?.name || '',
      nextPeriodName: snackInfo.nextPeriod?.name || ''
    });
  }, []);

  useEffect(() => {
    // 立即更新一次
    updateCountdown();

    // 设置定时器
    const interval = setInterval(updateCountdown, updateInterval);

    // 清理函数
    return () => {
      clearInterval(interval);
    };
  }, [updateCountdown, updateInterval]);

  return countdownInfo;
};

/**
 * 格式化倒计时文本
 */
export const formatCountdownText = (snackInfo: SnackTimeInfo): string => {
  if (snackInfo.isSnackTime) {
    return `现在是${snackInfo.currentPeriod?.name}时间，适合补充营养`;
  }
  
  return `${snackInfo.nextPeriod?.name}将在${snackInfo.timeUntilNext}开始`;
};

/**
 * 获取倒计时状态样式
 */
export const getCountdownStyle = (isSnackTime: boolean) => {
  if (isSnackTime) {
    return {
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-700',
      icon: '✅'
    };
  }
  
  return {
    bgColor: 'bg-amber-50',
    borderColor: 'border-amber-200', 
    textColor: 'text-amber-700',
    icon: '⏰'
  };
};
